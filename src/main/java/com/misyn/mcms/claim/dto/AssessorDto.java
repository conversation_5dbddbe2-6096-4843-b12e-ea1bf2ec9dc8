package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
import java.math.BigDecimal;
public class AssessorDto implements Serializable {
    private int refNo = AppConstant.ZERO_INT;
    private String paraType = AppConstant.STRING_EMPTY;
    private String code = AppConstant.STRING_EMPTY;
    private String name = AppConstant.STRING_EMPTY;
    private DistrictDto districtDto = new DistrictDto();
    private String updateFlag = AppConstant.STRING_EMPTY;
    private String insertFlag = AppConstant.STRING_EMPTY;
    private String assessorContactNo = AppConstant.STRING_EMPTY;
    private String userName = AppConstant.STRING_EMPTY;
    private String reportingCode = AppConstant.STRING_EMPTY;
    private String reportingToName = AppConstant.STRING_EMPTY;
    private String firstName = AppConstant.STRING_EMPTY;
    private String rteMobileNo = AppConstant.STRING_EMPTY;
    private String lastName = AppConstant.STRING_EMPTY;
    private BigDecimal totalOtherAmount = BigDecimal.ZERO;
    private BigDecimal totalScheduleAmount = BigDecimal.ZERO;

    //    for History
    private Integer claimNo;
    private Integer jobRefNo;
    private String action;


    public int getRefNo() {
        return refNo;
    }

    public void setRefNo(int refNo) {
        this.refNo = refNo;
    }

    public String getParaType() {
        return paraType;
    }

    public void setParaType(String paraType) {
        this.paraType = paraType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public DistrictDto getDistrictDto() {
        return districtDto;
    }

    public void setDistrictDto(DistrictDto districtDto) {
        this.districtDto = districtDto;
    }

    public String getUpdateFlag() {
        return updateFlag;
    }

    public void setUpdateFlag(String updateFlag) {
        this.updateFlag = updateFlag;
    }

    public String getInsertFlag() {
        return insertFlag;
    }

    public void setInsertFlag(String insertFlag) {
        this.insertFlag = insertFlag;
    }

    public String getAssessorContactNo() {
        return assessorContactNo;
    }

    public void setAssessorContactNo(String assessorContactNo) {
        this.assessorContactNo = assessorContactNo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getReportingCode() {
        return reportingCode;
    }

    public void setReportingCode(String reportingCode) {
        this.reportingCode = reportingCode;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getReportingToName() {
        return reportingToName;
    }

    public void setReportingToName(String reportingToName) {
        this.reportingToName = reportingToName;
    }

    public String getRteMobileNo() {
        return rteMobileNo;
    }

    public void setRteMobileNo(String rteMobileNo) {
        this.rteMobileNo = rteMobileNo;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public BigDecimal getTotalOtherAmount() {
        return totalOtherAmount;
    }

    public void setTotalOtherAmount(BigDecimal totalOtherAmount) {
        this.totalOtherAmount = totalOtherAmount;
    }

    public BigDecimal getTotalScheduleAmount() {
        return totalScheduleAmount;
    }

    public void setTotalScheduleAmount(BigDecimal totalScheduleAmount) {
        this.totalScheduleAmount = totalScheduleAmount;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getJobRefNo() {
        return jobRefNo;
    }

    public void setJobRefNo(Integer jobRefNo) {
        this.jobRefNo = jobRefNo;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }
}
