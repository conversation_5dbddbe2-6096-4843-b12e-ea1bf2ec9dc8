package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class CalculationSheetHistoryDto implements Serializable {

    private Integer calsheetId;
    private int calsheetTypeId;
    private String calsheetTypeDesc;
    private BigDecimal paidAmount;
    private String dateOfPaid;
    private String claimHandlerUserId;
    private String spteadmUserId;
    private String acrApprovedRte;
    private String approvedRte;
    private String rteAction;
    private String approvedUserId;
    private String voucherGeneratedUser;
    private int paymentStatus;

    public Integer getCalsheetId() {
        return calsheetId;
    }

    public void setCalsheetId(Integer calsheetId) {
        this.calsheetId = calsheetId;
    }

    public int getCalsheetTypeId() {
        return calsheetTypeId;
    }

    public void setCalsheetTypeId(int calsheetTypeId) {
        this.calsheetTypeId = calsheetTypeId;
    }

    public String getCalsheetTypeDesc() {
        return calsheetTypeDesc;
    }

    public void setCalsheetTypeDesc(String calsheetTypeDesc) {
        this.calsheetTypeDesc = calsheetTypeDesc;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public String getDateOfPaid() {
        return dateOfPaid;
    }

    public void setDateOfPaid(String dateOfPaid) {
        this.dateOfPaid = dateOfPaid;
    }

    public String getClaimHandlerUserId() {
        return claimHandlerUserId;
    }

    public void setClaimHandlerUserId(String claimHandlerUserId) {
        this.claimHandlerUserId = claimHandlerUserId;
    }

    public String getSpteadmUserId() {
        return spteadmUserId;
    }

    public void setSpteadmUserId(String spteadmUserId) {
        this.spteadmUserId = spteadmUserId;
    }

    public String getApprovedUserId() {
        return approvedUserId;
    }

    public void setApprovedUserId(String approvedUserId) {
        this.approvedUserId = approvedUserId;
    }

    public int getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(int paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getApprovedRte() {
        return approvedRte;
    }

    public void setApprovedRte(String approvedRte) {
        this.approvedRte = approvedRte;
    }

    public String getRteAction() {
        return rteAction;
    }

    public void setRteAction(String rteAction) {
        this.rteAction = rteAction;
    }

    public String getAcrApprovedRte() {
        return acrApprovedRte;
    }

    public void setAcrApprovedRte(String acrApprovedRte) {
        this.acrApprovedRte = acrApprovedRte;
    }

    public String getVoucherGeneratedUser() {
        return voucherGeneratedUser;
    }

    public void setVoucherGeneratedUser(String voucherGeneratedUser) {
        this.voucherGeneratedUser = voucherGeneratedUser;
    }
}
