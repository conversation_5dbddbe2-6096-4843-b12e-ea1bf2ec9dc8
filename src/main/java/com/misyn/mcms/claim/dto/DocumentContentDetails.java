package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor

public class DocumentContentDetails {
    private Integer documentContentId = AppConstant.ZERO_INT;
    private String documentBody = AppConstant.STRING_EMPTY;
    private String documentStatus = AppConstant.STRING_EMPTY;
    private String documentSubject = AppConstant.STRING_EMPTY;



//    public Integer getDocumentContentId () {
//        return documentContentId;
//    }
//
//    public void setDocumentContentId (Integer documentContentId) {
//        this.documentContentId  = documentContentId ;
//    }
//
//    public String getDocumentBody() {
//        return documentBody;
//    }
//
//    public void setDocumentBody(String documentBodyBody) {
//        this.documentBody = documentBodyBody;
//    }
//
//    public String getStatus() {
//        return documentStatus;
//    }
//
//    public void setStatus(String status) {
//        this.documentStatus = documentStatus;
//    }
//
//    public String getSubject() {
//        return documentSubject;
//    }
//
//    public void setSubject(String subject) {
//        this.documentSubject = documentSubject;
//    }

}
