package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.ClaimHandlerDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.AdvanceRequestStatusEnum;
import com.misyn.mcms.claim.enums.ClaimStatus;
import com.misyn.mcms.claim.enums.PolicyChannelType;
import com.misyn.mcms.claim.enums.YesNoWantDecideEnum;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
public class ClaimHandlerDaoImpl extends BaseAbstract<ClaimHandlerDaoImpl> implements ClaimHandlerDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimHandlerDaoImpl.class);

    @Override
    public ClaimHandlerDto insertMaster(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(SQL_INSERT_INTO_CLAIM_ASSIGN_ASSESSOR)) {
            ps.setInt(++index, claimHandlerDto.getClaimNo());
            ps.setInt(++index, claimHandlerDto.getTeamId());
            ps.setString(++index, claimHandlerDto.getAssignUserId());
            ps.setString(++index, claimHandlerDto.getAssignStatus());
            ps.setString(++index, claimHandlerDto.getAssignDateTime());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAssignUserId());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAssignDateTime());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAprvUserId());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAprvStatus());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAprvDateTime());
            ps.setString(++index, claimHandlerDto.getLiabilityAprvAssignUser());
            ps.setString(++index, claimHandlerDto.getLiabilityAprvAssignDateTime());
            ps.setString(++index, claimHandlerDto.getLiabilityAprvUser());
            ps.setString(++index, claimHandlerDto.getLiabilityAprvStatus());
            ps.setString(++index, claimHandlerDto.getLiabilityAprvDateTime());
            ps.setString(++index, claimHandlerDto.getManagerUserId());
            ps.setString(++index, claimHandlerDto.getChannelCode());
            ps.setInt(++index, claimHandlerDto.getAccessUserType());
            ps.setInt(++index, claimHandlerDto.getClaimStatus());
            ps.setBigDecimal(++index, claimHandlerDto.getReserveAmount());
            ps.setBigDecimal(++index, claimHandlerDto.getReserveAmountAfterAprv());
            ps.setBigDecimal(++index, claimHandlerDto.getAprvTotAcrAmount());
            ps.setString(++index, claimHandlerDto.getAcrAprvUser());
            ps.setBigDecimal(++index, claimHandlerDto.getEngineerApprovedAmount());
            ps.setBigDecimal(++index, claimHandlerDto.getLabourCost());
            ps.setBigDecimal(++index, claimHandlerDto.getPartCost());
            ps.setInt(++index, claimHandlerDto.getLossType());
            ps.setString(++index, claimHandlerDto.getIsAllDocUpload());
            ps.setString(++index, claimHandlerDto.getIsCheckAllMndDocs());
            ps.setBigDecimal(++index, claimHandlerDto.getPenaltyBaldTyre());
            ps.setBigDecimal(++index, claimHandlerDto.getPenaltyBaldTyreRate());
            ps.setString(++index, claimHandlerDto.getPenaltyBaldTyreUser());
            ps.setString(++index, claimHandlerDto.getPenaltyBaldTyreDateTime());
            ps.setBigDecimal(++index, claimHandlerDto.getPenaltyUnderInsurce());
            ps.setBigDecimal(++index, claimHandlerDto.getPenaltyUnderInsurceRate());
            ps.setString(++index, claimHandlerDto.getPenaltyUnderInsurceUser());
            ps.setString(++index, claimHandlerDto.getPenaltyUnderInsurceDateTime());
            ps.setString(++index, claimHandlerDto.getIntimationChk());
            ps.setString(++index, claimHandlerDto.getIntimationChkUser());
            ps.setString(++index, claimHandlerDto.getIntimationChkDateTime());
            ps.setString(++index, claimHandlerDto.getIsLcChk1());
            ps.setString(++index, claimHandlerDto.getIsLcChk2());
            ps.setString(++index, claimHandlerDto.getIsLcChk3());
            ps.setString(++index, claimHandlerDto.getIsLcChk4());
            ps.setString(++index, claimHandlerDto.getIsLcChk5());
            ps.setString(++index, claimHandlerDto.getIsLcChk6());
            ps.setString(++index, claimHandlerDto.getIsLcChk7());
            ps.setString(++index, claimHandlerDto.getIsLcChk8());
            ps.setString(++index, claimHandlerDto.getLcChkUser());
            ps.setString(++index, claimHandlerDto.getLcChkDateTime());
            ps.setString(++index, claimHandlerDto.getClaimPanelAssignUsers());
            ps.setString(++index, claimHandlerDto.getClaimPanelAssignUserDateTime());
            ps.setInt(++index, claimHandlerDto.getDecisionApproveClaimPanel());
            ps.setString(++index, claimHandlerDto.getClaimPanelDecision());
            ps.setString(++index, claimHandlerDto.getClaimPanelDecisionDateTime());
            ps.setInt(++index, claimHandlerDto.getRepudiatedType());
            ps.setString(++index, claimHandlerDto.getIsPrintRepudiatedLetter());
            ps.setString(++index, claimHandlerDto.getRepudiatedLetterPrintUserId());
            ps.setString(++index, claimHandlerDto.getRepudiatedLetterPrintDateTime());
            ps.setString(++index, claimHandlerDto.getFinancialInterest());
            ps.setInt(++index, claimHandlerDto.getLeasingRefNo());
            ps.setString(++index, claimHandlerDto.getFinalizeUserId());
            ps.setString(++index, claimHandlerDto.getFinalizeDateTime());
            ps.setString(++index, claimHandlerDto.getIsGenarateSupplyOrder());
            ps.setString(++index, claimHandlerDto.getSupplyOrderAssignStatus());
            ps.setString(++index, claimHandlerDto.getSupplyOrderAssignUser());
            ps.setString(++index, claimHandlerDto.getSupplyOrderAssignDateTime());
            ps.setString(++index, claimHandlerDto.getSupplyOrderCreateUser());
            ps.setString(++index, claimHandlerDto.getSupplyOrderCreateDateTime());
            ps.setString(++index, claimHandlerDto.getSupplyOrderCreateClose());
            ps.setString(++index, claimHandlerDto.getIsFileStore());
            ps.setString(++index, claimHandlerDto.getFileUserStoreUserId());
            ps.setString(++index, claimHandlerDto.getFileStoreDateTime());
            ps.setString(++index, claimHandlerDto.getReopenAssignUserId());
            ps.setString(++index, claimHandlerDto.getReopenAssignUserDateTime());
            ps.setString(++index, claimHandlerDto.getReopenUserId());
            ps.setString(++index, claimHandlerDto.getReopenDateTime());
            ps.setInt(++index, claimHandlerDto.getReopenNoOfTime());
            ps.setString(++index, claimHandlerDto.getIsGenFinalRemindLetter());
            ps.setString(++index, claimHandlerDto.getGenFinalRemindLetterDateTime());
            ps.setString(++index, claimHandlerDto.getDecisionMakingAssignUserId());
            ps.setString(++index, claimHandlerDto.getDecisionMakingAssignDateTime());
            ps.setString(++index, claimHandlerDto.getInvestigationStatus());
            ps.setString(++index, claimHandlerDto.getInvestigationAssignUserId());
            ps.setString(++index, claimHandlerDto.getInvestigationAssignDateTime());
            ps.setString(++index, claimHandlerDto.getInvestigationArrangeUserId());
            ps.setString(++index, claimHandlerDto.getInvestigationArrangeDateTime());
            ps.setString(++index, claimHandlerDto.getInvestigationCompletedUserId());
            ps.setString(++index, claimHandlerDto.getInvestigationCompletedDateTime());
            ps.setString(++index, claimHandlerDto.getSpecialApprovalInputUserId());
            ps.setString(++index, claimHandlerDto.getSpecialApprovalInputDateTime());
            ps.setString(++index, claimHandlerDto.getSpecialApprovalUserId());
            ps.setString(++index, claimHandlerDto.getSpecialApprovalDateTime());
            ps.setInt(++index, claimHandlerDto.getOldClaimStatus());
            ps.setInt(++index, claimHandlerDto.getOldEngineerClaimStatus());
            ps.setString(++index, claimHandlerDto.getIsDoubt());
            ps.setString(++index, claimHandlerDto.getIsOnSiteOffer());
            ps.setBigDecimal(++index, claimHandlerDto.getAprvAdvanceAmount());
            ps.setString(++index, claimHandlerDto.getReOpenType());
            ps.setString(++index, claimHandlerDto.getCloseStatus());
            ps.setString(++index, claimHandlerDto.getCloseUser());
            ps.setString(++index, claimHandlerDto.getCloseDateTime());
            ps.setInt(++index, claimHandlerDto.getRepudiatedLetterType());
            ps.setString(++index, claimHandlerDto.getInpStatus());
            ps.setString(++index, claimHandlerDto.getInpUserId());
            ps.setString(++index, claimHandlerDto.getInpDateTime());
            ps.setInt(++index, claimHandlerDto.getVersionNo());
            ps.setString(++index, claimHandlerDto.getIsExcessInclude());
            ps.setString(++index, claimHandlerDto.getIsProvideOffer());
            ps.setString(++index, claimHandlerDto.getLetterPanelUserId());
            ps.setString(++index, claimHandlerDto.getLetterPanelDateTime());
            ps.setString(++index, claimHandlerDto.getIsRejectionAttached());
            ps.setString(++index, claimHandlerDto.getRejectionAttachedDateTime());
            ps.setString(++index, claimHandlerDto.getAdvanceApprovalAssignUser());
            ps.setString(++index, claimHandlerDto.getAdvanceApprovalAssignDateTime());
            ps.setString(++index, claimHandlerDto.getAdvanceStatus());
            ps.setString(++index, claimHandlerDto.getAdvanceForwardedUser());
            ps.setBigDecimal(++index, claimHandlerDto.getPendingAdvance());
            ps.setString(++index, claimHandlerDto.getAdvanceApprovedUser());
            ps.setString(++index, claimHandlerDto.getAdvanceApprovedDateTime());
            ps.setString(++index, claimHandlerDto.getForwardedEngineer());
            if (ps.executeUpdate() > 0) {
                return claimHandlerDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error");
        }
        return null;
    }

    @Override
    public ClaimHandlerDto updateMaster(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto insertTemporary(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto updateTemporary(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        return null;
    }

    @Override
    public ClaimHandlerDto insertHistory(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimHandlerDto searchMaster(Connection connection, Object id) throws Exception {
        ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SEARCH_CLAIMS_BY_N_TXN_NO);
            ps.setInt(1, (Integer) id);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimHandlerDto = getClaimById(rs);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimHandlerDto;
    }

    @Override
    public ClaimHandlerDto searchMasterByClaimNo(Connection connection, Integer claimNo) throws Exception {
        ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SEARCH_CLAIMS_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimHandlerDto = getClaimById(rs);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimHandlerDto;
    }

    @Override
    public boolean updateFinancialInterest(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_FINANCIAL_INTEREST)) {
            ps.setString(++index, claimHandlerDto.getFinancialInterest());
            ps.setInt(++index, claimHandlerDto.getLeasingRefNo());
            ps.setInt(++index, claimHandlerDto.getClaimNo());

            if (ps.executeUpdate() > 0) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return false;
    }

    @Override
    public boolean updateLiabilityCheckList(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_LIABILITY_CHECK_LIST)) {
            ps.setString(++index, claimHandlerDto.getIsLcChk1());
            ps.setString(++index, claimHandlerDto.getIsLcChk2());
            ps.setString(++index, claimHandlerDto.getIsLcChk3());
            ps.setString(++index, claimHandlerDto.getIsLcChk4());
            ps.setString(++index, claimHandlerDto.getIsLcChk5());
            ps.setString(++index, claimHandlerDto.getIsLcChk6());
            ps.setString(++index, claimHandlerDto.getIsLcChk7());
            ps.setString(++index, claimHandlerDto.getIsLcChk8());
            ps.setString(++index, claimHandlerDto.getInpUserId());
            ps.setString(++index, claimHandlerDto.getInpDateTime());
            ps.setInt(++index, claimHandlerDto.getClaimNo());

            if (ps.executeUpdate() > 0) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return false;
    }

    @Override
    public ClaimHandlerDto updateInitialLiabilityUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_INITIAL_LIABILITY_USER)) {
            ps.setString(++index, claimHandlerDto.getInitLiabilityAssignDateTime());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAssignUserId());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAprvStatus());
            ps.setInt(++index, claimHandlerDto.getClaimNo());

            if (ps.executeUpdate() > 0) {
                return claimHandlerDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimHandlerDto updateAssignUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_ASSIGN_USER)) {
            ps.setString(++index, claimHandlerDto.getAssignDateTime());
            ps.setString(++index, claimHandlerDto.getAssignUserId());
            ps.setString(++index, claimHandlerDto.getAssignStatus());
            ps.setString(++index, claimHandlerDto.getAssignUserId());
            ps.setString(++index, claimHandlerDto.getAssignDateTime());
            ps.setInt(++index, claimHandlerDto.getClaimNo());

            if (ps.executeUpdate() > 0) {
                return claimHandlerDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimHandlerDto updateLiabilityAprvAssignUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_LIABILITY_APRV_ASSIGN_USER)) {
            ps.setString(++index, claimHandlerDto.getLiabilityAprvAssignDateTime());
            ps.setString(++index, claimHandlerDto.getLiabilityAprvAssignUser());
            ps.setInt(++index, claimHandlerDto.getClaimNo());

            if (ps.executeUpdate() > 0) {
                return claimHandlerDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimHandlerDto updateInitialLiabilityAprvStatus(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_INITIAL_LIABILITY_APRV_STATUS)) {
            ps.setString(++index, claimHandlerDto.getInitLiabilityAprvDateTime());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAprvUserId());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAprvStatus());
            ps.setInt(++index, claimHandlerDto.getClaimNo());

            if (ps.executeUpdate() > 0) {
                return claimHandlerDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimHandlerDto updateInvestigationCompletedUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement("UPDATE claim_assign_claim_handler\n"
                + "	SET\n"
                + "		V_INVESTIGATION_STATUS=?,\n"
                + "		V_INVESTIGATION_COMPLETED_USER_ID=?,\n"
                + "		D_INVESTIGATION_COMPLETED_DATE_TIME=?,\n"
                + "		N_CLAIM_STATUS =?\n"
                + "	WHERE N_CLAIM_NO = ?")) {
            ps.setString(++index, claimHandlerDto.getInvestigationStatus());
            ps.setString(++index, claimHandlerDto.getInvestigationCompletedUserId());
            ps.setString(++index, claimHandlerDto.getInvestigationCompletedDateTime());
            ps.setInt(++index, claimHandlerDto.getClaimStatus());
            ps.setInt(++index, claimHandlerDto.getClaimNo());

            if (ps.executeUpdate() > 0) {
                return claimHandlerDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimHandlerDto updateInvestigationArrangedUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement("UPDATE claim_assign_claim_handler\n"
                    + "	SET\n"
                    + "		V_INVESTIGATION_STATUS=?,\n"
                    + "		V_INVESTIGATION_ARRANGE_USER_ID=?,\n"
                    + "		D_INVESTIGATION_ARRANGE_DATE_TIME=?,\n"
                    + "		N_CLAIM_STATUS =?\n"
                    + "	WHERE N_CLAIM_NO = ?");
            ps.setString(++index, claimHandlerDto.getInvestigationStatus());
            ps.setString(++index, claimHandlerDto.getInvestigationArrangeUserId());
            ps.setString(++index, claimHandlerDto.getInvestigationArrangeDateTime());
            ps.setInt(++index, claimHandlerDto.getClaimStatus());
            ps.setInt(++index, claimHandlerDto.getClaimNo());
            if (ps.executeUpdate() > 0) return claimHandlerDto;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public void updateInvestigationAssignUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement("UPDATE claim_assign_claim_handler\n"
                    + "	SET\n"
                    + "		V_INVESTIGATION_STATUS=?,\n"
                    + "		V_INVESTIGATION_ASSIGN_USER_ID=?,\n"
                    + "		D_INVESTIGATION_ASSIGN_DATE_TIME=?\n"
                    + "	WHERE N_CLAIM_NO = ?");
            ps.setString(++index, claimHandlerDto.getInvestigationStatus());
            ps.setString(++index, claimHandlerDto.getInvestigationAssignUserId());
            ps.setString(++index, claimHandlerDto.getInvestigationAssignDateTime());
            ps.setInt(++index, claimHandlerDto.getClaimNo());

            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }

    }

    @Override
    public void updateInvestigationAssignUserAndDecisionMakeUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement("UPDATE claim_assign_claim_handler\n"
                    + "	SET\n"
                    + "		V_INVESTIGATION_STATUS=?,\n"
                    + "		V_INVESTIGATION_ASSIGN_USER_ID=?,\n"
                    + "		D_INVESTIGATION_ASSIGN_DATE_TIME=?,\n"
                    + "		V_DECISION_MAKING_ASSIGN_USER_ID=?,\n"
                    + "		D_DECISION_MAKING_ASSIGN_DATE_TIME=?\n"
                    + "	WHERE N_CLAIM_NO = ?");
            ps.setString(++index, claimHandlerDto.getInvestigationStatus());
            ps.setString(++index, claimHandlerDto.getInvestigationAssignUserId());
            ps.setString(++index, claimHandlerDto.getInvestigationAssignDateTime());
            ps.setString(++index, claimHandlerDto.getDecisionMakingAssignUserId());
            ps.setString(++index, claimHandlerDto.getDecisionMakingAssignDateTime());
            ps.setInt(++index, claimHandlerDto.getClaimNo());

            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateInvestigationStatus(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement("UPDATE claim_assign_claim_handler\n"
                    + "	SET\n"
                    + "		V_INVESTIGATION_STATUS=?\n"
                    + "	WHERE N_CLAIM_NO = ?");
            ps.setString(++index, claimHandlerDto.getInvestigationStatus());
            ps.setInt(++index, claimHandlerDto.getClaimNo());
            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public ClaimHandlerDto updateLiabilityAprvStatus(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_LIABILITY_APRV_STATUS);
            ps.setString(++index, claimHandlerDto.getLiabilityAprvDateTime());
            ps.setString(++index, claimHandlerDto.getLiabilityAprvUser());
            ps.setString(++index, claimHandlerDto.getLiabilityAprvStatus());
            ps.setInt(++index, claimHandlerDto.getClaimNo());

            if (ps.executeUpdate() > 0) {
                return claimHandlerDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public void updateClaimStatus(Connection connection, int claimNo, int status) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_STATUS);
            ps.setInt(++index, status);
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateStoreStatus(Connection connection, int claimNo, String status) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_STORE_STATUS);
            ps.setString(++index, status);
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateStoreStatusAndUserID(Connection connection, int claimNo,String status, UserDto user) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_STORE_USER);
            ps.setString(++index, status);
            ps.setString(++index, user.getUserId());
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateAssignStatus(Connection connection, int claimNo, String status) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_ASSIGN_STATUS);
            ps.setString(++index, status);
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public DataGridDto getClaimHandlerPanelDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.D_ACCID_DATE BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SEL_SQL = type == 2 ? SQL_SELECT_TO_MAIN_PANEL_LIST.concat(SQL_SEARCH).concat(SQL_ORDER) : SQL_SELECT_TO_PANEL_LIST.concat(SQL_SEARCH).concat(SQL_ORDER);

        final String COUNT_SQL = type == 2 ? SQL_SELECT_COUNT_TO_MAIN_PANEL_LIST.concat(SQL_SEARCH) : SQL_SELECT_COUNT_TO_PANEL_LIST.concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ClaimHandlerGridDto claimHandlerDto = new ClaimHandlerGridDto();
                    claimHandlerDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    claimHandlerDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    claimHandlerDto.setPolicyNumberValue(rs.getString("t1.V_POL_NUMBER"));
                    claimHandlerDto.setInspectionStatus(0);
                    claimHandlerDto.setAssignDateTime(rs.getString("t1.D_ACCID_DATE"));
                    claimHandlerDto.setDocumentStatus(null == rs.getString("t2.V_IS_ALL_DOC_UPLOAD") ? AppConstant.NO : rs.getString("t2.V_IS_ALL_DOC_UPLOAD"));
                    claimHandlerDto.setDocumentCheck(null == rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS") ? AppConstant.NO : rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS"));
                    claimHandlerDto.setLiabilityStatus(null == rs.getString("t2.V_LIABILITY_APRV_STATUS") ? AppConstant.NO : rs.getString("t2.V_LIABILITY_APRV_STATUS"));
                    claimHandlerDto.setClaimStatus(rs.getInt("t2.N_CLAIM_STATUS"));
                    claimHandlerDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                    claimHandlerDto.setTxnId(rs.getInt("t2.N_TXN_NO"));
                    claimHandlerDto.setAcr(rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT").toString());
                    claimHandlerDto.setPresentReverseAmount(rs.getBigDecimal("t2.N_RESERVE_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_RESERVE_AMOUNT").toString());
                    claimHandlerDto.setFileStore(rs.getString("t2.V_IS_FILE_STORE"));
                    claimHandlerDto.setPartialLoss(rs.getInt("t2.N_LOSS_TYPE"));
                    claimHandlerDto.setLiabilityAssignUser(rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER"));
                    claimHandlerDto.setLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT).equals(AppConstant.DEFAULT_DATE)
                            ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT));

                    claimHandlerDto.setIntLiabilityAssignUser(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID"));

                    claimHandlerDto.setIntLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                    claimHandlerDto.setInvestigationStatus(rs.getString("t2.V_INVESTIGATION_STATUS"));

                    claimHandlerDto.setIsDoubt(rs.getString("t2.V_IS_DOUBT"));
                    claimHandlerDto.setIsOnSiteOffer(rs.getString("t2.V_IS_ON_SITE_OFFER"));
                    claimHandlerDto.setAprvAdvanceAmount(rs.getBigDecimal("t2.N_APRV_ADVANCE_AMOUNT"));
                    claimHandlerDto.setReOpenType(rs.getString("t2.V_REOPEN_TYPE"));
                    claimHandlerDto.setCloseStatus(rs.getString("t2.V_CLOSE_STATUS"));
                    claimHandlerDto.setCloseUser(rs.getString("t2.V_CLOSE_USER"));
                    claimHandlerDto.setCloseDateTime(rs.getString("t2.D_CLOSE_DATE_TIME"));

                    if (null != rs.getString("t2.V_CLOSE_STATUS") && rs.getString("t2.V_CLOSE_STATUS").equals("CLOSE")) {
                        claimHandlerDto.setFinalizeStatus(AppConstant.YES);
                    } else {
                        claimHandlerDto.setFinalizeStatus(AppConstant.NO);
                    }

                    claimHandlerDto.setIndex(++index);
                    handlerList.add(claimHandlerDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public ClaimHandlerDto updateInitialLiabilityAprvDetails(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_INITIAL_LIABILITY_APRV_DETAILS);
            ps.setInt(++index, claimHandlerDto.getClaimNo());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAssignUserId());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAprvUserId());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAprvDateTime());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAssignDateTime());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAprvStatus());

            if (ps.executeUpdate() > 0) {
                return claimHandlerDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public void updateDecisionMakingUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_DECISION_MAKING_ASSIGN_USER);
            ps.setString(++index, claimHandlerDto.getDecisionMakingAssignDateTime());
            ps.setString(++index, claimHandlerDto.getDecisionMakingAssignUserId());
            ps.setInt(++index, claimHandlerDto.getClaimNo());

            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateRepudiatedReason(Connection connection, Integer claimNo, Integer repudiatedReason) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_REPUDIATED_TYPE);
            ps.setInt(++index, repudiatedReason);
            ps.setInt(++index, claimNo);

            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }

    }

    @Override
    public void updateUploadAllDocumentStatus(Connection connection, int claimNo, YesNoWantDecideEnum isAllDocUpload) throws MisynJDBCException {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_IS_ALL_DOC_UPLOAD_DOCS);
            ps.setString(++index, isAllDocUpload.getYesNoWantDecide());
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public void updateCheckedMandatoryDocumentStatus(Connection connection, int claimNo, YesNoWantDecideEnum isCheckAllMndDocs) throws MisynJDBCException {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_IS_CHECK_ALL_MND_DOCS);
            ps.setString(++index, isCheckAllMndDocs.getYesNoWantDecide());
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer priority) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String[] orderFields = {orderField};
        Object[] instances = {AppConstant.PRIORITY_HIGH, AppConstant.PRIORITY_NORMAL};
        final String SQL_ORDER = null != priority ? formatOrderSQL(start, length, orderType, orderField).toString() : orderByCases(start, length, orderType, "t1.V_PRIORITY", orderFields, instances).toString();

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.D_ACCID_DATE BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);

        final String COUNT_SQL = SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ClaimHandlerGridDto claimHandlerDto = new ClaimHandlerGridDto();
                    claimHandlerDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    claimHandlerDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    claimHandlerDto.setPolicyNumberValue(rs.getString("t1.V_POL_NUMBER"));
                    claimHandlerDto.setInspectionStatus(0);
                    claimHandlerDto.setAccidentDate(rs.getString("t1.D_ACCID_DATE"));
                    claimHandlerDto.setAssignDateTime(Utility.getDate(rs.getString("t2.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                    claimHandlerDto.setDocumentStatus(null == rs.getString("t2.V_IS_ALL_DOC_UPLOAD") ? AppConstant.NO : rs.getString("t2.V_IS_ALL_DOC_UPLOAD"));
                    claimHandlerDto.setDocumentCheck(null == rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS") ? AppConstant.NO : rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS"));
                    claimHandlerDto.setLiabilityStatus(null == rs.getString("t2.V_LIABILITY_APRV_STATUS") ? AppConstant.NO : rs.getString("t2.V_LIABILITY_APRV_STATUS"));
                    claimHandlerDto.setClaimStatus(rs.getInt("t2.N_CLAIM_STATUS"));
                    claimHandlerDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                    claimHandlerDto.setTxnId(rs.getInt("t2.N_TXN_NO"));
                    claimHandlerDto.setAcr(rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT").toString());
                    claimHandlerDto.setPresentReverseAmount(rs.getBigDecimal("t2.N_RESERVE_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_RESERVE_AMOUNT").toString());
                    claimHandlerDto.setFileStore(rs.getString("t2.V_IS_FILE_STORE"));
                    claimHandlerDto.setPartialLoss(rs.getInt("t2.N_LOSS_TYPE"));
                    claimHandlerDto.setAssignUser(rs.getString("t2.V_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_ASSIGN_USER_ID"));
                    claimHandlerDto.setLiabilityAssignUser(rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER"));
                    claimHandlerDto.setLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT).equals(AppConstant.DEFAULT_DATE)
                            ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT));

                    claimHandlerDto.setIntLiabilityAssignUser(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID"));

                    claimHandlerDto.setIntLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                    claimHandlerDto.setInvestigationStatus(rs.getString("t2.V_INVESTIGATION_STATUS"));

                    claimHandlerDto.setIsDoubt(rs.getString("t2.V_IS_DOUBT"));
                    claimHandlerDto.setIsOnSiteOffer(rs.getString("t2.V_IS_ON_SITE_OFFER"));
                    claimHandlerDto.setAprvAdvanceAmount(rs.getBigDecimal("t2.N_APRV_ADVANCE_AMOUNT"));
                    claimHandlerDto.setReOpenType(rs.getString("t2.V_REOPEN_TYPE"));
                    claimHandlerDto.setCloseStatus(rs.getString("t2.V_CLOSE_STATUS"));
                    claimHandlerDto.setCloseUser(rs.getString("t2.V_CLOSE_USER"));
                    claimHandlerDto.setCloseDateTime(rs.getString("t2.D_CLOSE_DATE_TIME"));
                    claimHandlerDto.setPolicyChannelType(null == rs.getString("t1.V_POLICY_CHANNEL_TYPE") || rs.getString("t1.V_POLICY_CHANNEL_TYPE").trim().isEmpty() ? PolicyChannelType.CONVENTIONAL.name() : rs.getString("t1.V_POLICY_CHANNEL_TYPE"));
                    claimHandlerDto.setPriority(null == rs.getString("t1.V_PRIORITY") || rs.getString("t1.V_PRIORITY").isEmpty() ? AppConstant.PRIORITY_NORMAL : rs.getString("t1.V_PRIORITY"));
                    if (null != rs.getString("t2.V_CLOSE_STATUS") && rs.getString("t2.V_CLOSE_STATUS").equals("CLOSE")) {
                        claimHandlerDto.setFinalizeStatus(AppConstant.YES);
                    } else {
                        claimHandlerDto.setFinalizeStatus(AppConstant.NO);
                    }

                    if (claimHandlerDto.getClaimStatus() == 43 || claimHandlerDto.getClaimStatus() == 46 || claimHandlerDto.getClaimStatus() == 48 || claimHandlerDto.getClaimStatus() == 52
                            || claimHandlerDto.getClaimStatus() == 53 || claimHandlerDto.getClaimStatus() == 54 || claimHandlerDto.getClaimStatus() == 55 || claimHandlerDto.getClaimStatus() == 56 || claimHandlerDto.getClaimStatus() == 68) {
                        claimHandlerDto.setClaimStatusDesc("Pending Decision");
                    }


                    claimHandlerDto.setIndex(++index);
                    handlerList.add(claimHandlerDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public DataGridDto getLetterPanelList(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String[] orderFields = {orderField};
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t2.D_LETTER_PANEL_DATETIME BETWEEN " + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SEL_SQL = SQL_SELECT_ALL_TO_L_PANEL.concat(SQL_SEARCH).concat(SQL_ORDER);

        final String COUNT_SQL = SQL_COUNT_LETTER_PANEL.concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ClaimHandlerGridDto claimHandlerDto = new ClaimHandlerGridDto();
                    claimHandlerDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    claimHandlerDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    claimHandlerDto.setIsfClaimNo(rs.getString("t1.V_ISF_CLAIM_NO"));
                    claimHandlerDto.setPolicyNumberValue(rs.getString("t1.V_POL_NUMBER"));
                    claimHandlerDto.setPolicyChannelType(rs.getString("t1.V_POLICY_CHANNEL_TYPE"));
                    claimHandlerDto.setdMakingAssignUid(null == rs.getString("t2.V_DECISION_MAKING_ASSIGN_USER_ID") ? AppConstant.EMPTY_STRING : rs.getString("t2.V_DECISION_MAKING_ASSIGN_USER_ID"));

                    String letterTypedesc = rs.getString("t3.V_REPUDIATE_LETTER_TYPE_DESC");
                    if (letterTypedesc.equals("Please Select")) {
                        claimHandlerDto.setRejectedReason(AppConstant.NOT_AVAILABLE);
                    } else {
                        claimHandlerDto.setRejectedReason(letterTypedesc);
                    }
                    claimHandlerDto.setAccidentDate(rs.getString("t1.D_ACCID_DATE"));

                    claimHandlerDto.setAssignDateTime(Utility.getDate(rs.getString("t2.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));

                    claimHandlerDto.setAcr(rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT").toString());

                    claimHandlerDto.setLiabilityAssignUser(rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER"));
                    claimHandlerDto.setLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT).equals(AppConstant.DEFAULT_DATE)
                            ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT));

                    claimHandlerDto.setIntLiabilityAssignUser(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID"));
                    claimHandlerDto.setIntLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                    claimHandlerDto.setPolicyChannelType(null == rs.getString("t1.V_POLICY_CHANNEL_TYPE") || rs.getString("t1.V_POLICY_CHANNEL_TYPE").trim().isEmpty() ? PolicyChannelType.CONVENTIONAL.name() : rs.getString("t1.V_POLICY_CHANNEL_TYPE"));
                    claimHandlerDto.setIsRejectionAttached(null == rs.getString("t2.V_IS_REJECTION_ATTACHED") ? AppConstant.EMPTY_STRING : rs.getString("t2.V_IS_REJECTION_ATTACHED"));

                    claimHandlerDto.setIndex(++index);
                    handlerList.add(claimHandlerDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String calsheetStatus) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER;
        if (null == calsheetStatus || AppConstant.EMPTY_STRING.equalsIgnoreCase(calsheetStatus)) {
            SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        } else {
            SQL_ORDER = formatOrderSQL(start, orderType, orderField).toString();
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.D_ACCID_DATE BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);

        final String COUNT_SQL = SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            } finally {

            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ClaimHandlerGridDto claimHandlerDto = new ClaimHandlerGridDto();
                    claimHandlerDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    claimHandlerDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    claimHandlerDto.setPolicyNumberValue(rs.getString("t1.V_POL_NUMBER"));
                    claimHandlerDto.setInspectionStatus(0);
                    claimHandlerDto.setAccidentDate(rs.getString("t1.D_ACCID_DATE"));
                    claimHandlerDto.setAssignDateTime(Utility.getDate(rs.getString("t2.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                    claimHandlerDto.setDocumentStatus(null == rs.getString("t2.V_IS_ALL_DOC_UPLOAD") ? AppConstant.NO : rs.getString("t2.V_IS_ALL_DOC_UPLOAD"));
                    claimHandlerDto.setDocumentCheck(null == rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS") ? AppConstant.NO : rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS"));
                    claimHandlerDto.setLiabilityStatus(null == rs.getString("t2.V_LIABILITY_APRV_STATUS") ? AppConstant.NO : rs.getString("t2.V_LIABILITY_APRV_STATUS"));
                    claimHandlerDto.setClaimStatus(rs.getInt("t2.N_CLAIM_STATUS"));
                    claimHandlerDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                    claimHandlerDto.setTxnId(rs.getInt("t2.N_TXN_NO"));
                    claimHandlerDto.setAcr(rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT").toString());
                    claimHandlerDto.setPresentReverseAmount(rs.getBigDecimal("t2.N_RESERVE_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_RESERVE_AMOUNT").toString());
                    claimHandlerDto.setFileStore(rs.getString("t2.V_IS_FILE_STORE"));
                    claimHandlerDto.setPartialLoss(rs.getInt("t2.N_LOSS_TYPE"));
                    claimHandlerDto.setAssignUser(rs.getString("t2.V_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_ASSIGN_USER_ID"));
                    claimHandlerDto.setLiabilityAssignUser(rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER"));
                    claimHandlerDto.setLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT).equals(AppConstant.DEFAULT_DATE)
                            ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT));

                    claimHandlerDto.setIntLiabilityAssignUser(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID"));

                    claimHandlerDto.setIntLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                    claimHandlerDto.setInvestigationStatus(rs.getString("t2.V_INVESTIGATION_STATUS"));

                    claimHandlerDto.setIsDoubt(rs.getString("t2.V_IS_DOUBT"));
                    claimHandlerDto.setIsOnSiteOffer(rs.getString("t2.V_IS_ON_SITE_OFFER"));
                    claimHandlerDto.setAprvAdvanceAmount(rs.getBigDecimal("t2.N_APRV_ADVANCE_AMOUNT"));
                    claimHandlerDto.setReOpenType(rs.getString("t2.V_REOPEN_TYPE"));
                    claimHandlerDto.setCloseStatus(rs.getString("t2.V_CLOSE_STATUS"));
                    claimHandlerDto.setCloseUser(rs.getString("t2.V_CLOSE_USER"));
                    claimHandlerDto.setCloseDateTime(rs.getString("t2.D_CLOSE_DATE_TIME"));
                    claimHandlerDto.setPolicyChannelType(null == rs.getString("t1.V_POLICY_CHANNEL_TYPE") || rs.getString("t1.V_POLICY_CHANNEL_TYPE").trim().isEmpty() ? PolicyChannelType.CONVENTIONAL.name() : rs.getString("t1.V_POLICY_CHANNEL_TYPE"));
                    if (null != rs.getString("t2.V_CLOSE_STATUS") && rs.getString("t2.V_CLOSE_STATUS").equals("CLOSE")) {
                        claimHandlerDto.setFinalizeStatus(AppConstant.YES);
                    } else {
                        claimHandlerDto.setFinalizeStatus(AppConstant.NO);
                    }

                    if (claimHandlerDto.getClaimStatus() == 43 || claimHandlerDto.getClaimStatus() == 46 || claimHandlerDto.getClaimStatus() == 48 || claimHandlerDto.getClaimStatus() == 52
                            || claimHandlerDto.getClaimStatus() == 53 || claimHandlerDto.getClaimStatus() == 54 || claimHandlerDto.getClaimStatus() == 55 || claimHandlerDto.getClaimStatus() == 56 || claimHandlerDto.getClaimStatus() == 68) {
                        claimHandlerDto.setClaimStatusDesc("Pending Decision");
                    }


                    claimHandlerDto.setIndex(++index);
                    handlerList.add(claimHandlerDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public DataGridDto getClaimHandlerSupplierDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String[] orderFields = {orderField};
        Object[] instances = {AppConstant.PRIORITY_HIGH, AppConstant.PRIORITY_NORMAL};
        final String SQL_ORDER = orderByCases(start, length, orderType, "t1.V_PRIORITY", orderFields, instances).toString();

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.D_ACCID_DATE BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SEL_SQL = SQL_SELECT_ALL_TO_SUPPLIER_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);

        final String COUNT_SQL = SQL_COUNT_SUPPLIER.concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    if (4 == type) {
                        if (isPendingInspectionFound(conn, rs.getInt("t1.N_CLIM_NO"))) {
                            --count;
                            continue;
                        }
                    } else if (50 == type) {
                        if (!isPendingInspectionFound(conn, rs.getInt("t1.N_CLIM_NO"))) {
                            --count;
                            continue;
                        }
                    }
                    ClaimHandlerGridDto claimHandlerDto = new ClaimHandlerGridDto();
                    claimHandlerDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    claimHandlerDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    claimHandlerDto.setPolicyNumberValue(rs.getString("t1.V_POL_NUMBER"));
                    claimHandlerDto.setInspectionStatus(0);
                    claimHandlerDto.setAccidentDate(rs.getString("t1.D_ACCID_DATE"));
                    claimHandlerDto.setPriority(null == rs.getString("t1.V_PRIORITY") || rs.getString("t1.V_PRIORITY").isEmpty() ? AppConstant.PRIORITY_NORMAL : rs.getString("t1.V_PRIORITY"));
                    claimHandlerDto.setAssignDateTime(Utility.getDate(rs.getString("t2.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                    claimHandlerDto.setDocumentStatus(null == rs.getString("t2.V_IS_ALL_DOC_UPLOAD") ? AppConstant.NO : rs.getString("t2.V_IS_ALL_DOC_UPLOAD"));
                    claimHandlerDto.setDocumentCheck(null == rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS") ? AppConstant.NO : rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS"));
                    claimHandlerDto.setLiabilityStatus(null == rs.getString("t2.V_LIABILITY_APRV_STATUS") ? AppConstant.NO : rs.getString("t2.V_LIABILITY_APRV_STATUS"));
                    claimHandlerDto.setClaimStatus(rs.getInt("t2.N_CLAIM_STATUS"));
                    claimHandlerDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                    claimHandlerDto.setTxnId(rs.getInt("t2.N_TXN_NO"));
                    claimHandlerDto.setAcr(rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT").toString());
                    claimHandlerDto.setPresentReverseAmount(rs.getBigDecimal("t2.N_RESERVE_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_RESERVE_AMOUNT").toString());
                    claimHandlerDto.setFileStore(rs.getString("t2.V_IS_FILE_STORE"));
                    claimHandlerDto.setPartialLoss(rs.getInt("t2.N_LOSS_TYPE"));
                    claimHandlerDto.setAssignUser(rs.getString("t2.V_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_ASSIGN_USER_ID"));
                    claimHandlerDto.setLiabilityAssignUser(rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER"));
                    claimHandlerDto.setLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT).equals(AppConstant.DEFAULT_DATE)
                            ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT));

                    claimHandlerDto.setIntLiabilityAssignUser(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID") == null || rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID").isEmpty() ? AppConstant.STRING_EMPTY : rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID"));

                    claimHandlerDto.setIntLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                    claimHandlerDto.setInvestigationStatus(rs.getString("t2.V_INVESTIGATION_STATUS"));

                    claimHandlerDto.setIsDoubt(rs.getString("t2.V_IS_DOUBT"));
                    claimHandlerDto.setIsOnSiteOffer(rs.getString("t2.V_IS_ON_SITE_OFFER"));
                    claimHandlerDto.setAprvAdvanceAmount(rs.getBigDecimal("t2.N_APRV_ADVANCE_AMOUNT"));
                    claimHandlerDto.setReOpenType(rs.getString("t2.V_REOPEN_TYPE"));
                    claimHandlerDto.setCloseStatus(rs.getString("t2.V_CLOSE_STATUS"));
                    claimHandlerDto.setCloseUser(rs.getString("t2.V_CLOSE_USER"));
                    claimHandlerDto.setCloseDateTime(rs.getString("t2.D_CLOSE_DATE_TIME"));
                    claimHandlerDto.setPolicyChannelType(null == rs.getString("t1.V_POLICY_CHANNEL_TYPE") || rs.getString("t1.V_POLICY_CHANNEL_TYPE").trim().isEmpty() ? PolicyChannelType.CONVENTIONAL.name() : rs.getString("t1.V_POLICY_CHANNEL_TYPE"));
                    if (null != rs.getString("t2.V_CLOSE_STATUS") && rs.getString("t2.V_CLOSE_STATUS").equals("CLOSE")) {
                        claimHandlerDto.setFinalizeStatus(AppConstant.YES);
                    } else {
                        claimHandlerDto.setFinalizeStatus(AppConstant.NO);
                    }

                    if (claimHandlerDto.getClaimStatus() == 43 || claimHandlerDto.getClaimStatus() == 46 || claimHandlerDto.getClaimStatus() == 48 || claimHandlerDto.getClaimStatus() == 52
                            || claimHandlerDto.getClaimStatus() == 53 || claimHandlerDto.getClaimStatus() == 54 || claimHandlerDto.getClaimStatus() == 55 || claimHandlerDto.getClaimStatus() == 56 || claimHandlerDto.getClaimStatus() == 68) {
                        claimHandlerDto.setClaimStatusDesc("Pending Decision");
                    }
                    claimHandlerDto.setSupplierStatus(rs.getString("t4.v_supply_order_status") == null || rs.getString("t4.v_supply_order_status").isEmpty() ? AppConstant.STRING_EMPTY : rs.getString("t4.v_supply_order_status"));
                    claimHandlerDto.setSupplierOrderAssignDateTime(Utility.getDate(rs.getString("t2.D_SUPPLY_ORDER_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_SUPPLY_ORDER_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));


                    claimHandlerDto.setIndex(++index);
                    handlerList.add(claimHandlerDto);

                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    private boolean isPendingInspectionFound(Connection conn, int claimNo) {
        try {
            PreparedStatement ps = conn.prepareStatement("SELECT 1 from rte_pending_claim_detail WHERE claim_no = ? limit 1");
            ps.setInt(1, claimNo);
            return ps.executeQuery().next();
        } catch (SQLException e) {
            LOGGER.error(e.getMessage(), e);
        }
        return false;

    }

    @Override
    public List<LeasingCompanyDto> getLeasingCompanyDetails(Connection connection) throws Exception {
        List<LeasingCompanyDto> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_LEASING_COMPANY_DETAILS);
            rs = ps.executeQuery();
            while (rs.next()) {
                LeasingCompanyDto leasingCompanyDto = new LeasingCompanyDto();
                leasingCompanyDto.setLeasingName(rs.getString("V_LEASING_COM_NAME"));
                leasingCompanyDto.setLeasingRefNo(rs.getInt("N_LEASING_REF_NO"));
                list.add(leasingCompanyDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public ClaimHandlerDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimHandlerDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    public ClaimHandlerDto getClaimById(ResultSet rs) {
        ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
        try {
            claimHandlerDto.setTxnNo(rs.getInt("N_TXN_NO"));
            claimHandlerDto.setClaimNo(rs.getInt("N_CLAIM_NO"));
            claimHandlerDto.setTeamId(rs.getInt("N_TEAM_ID"));
            claimHandlerDto.setAssignUserId(rs.getString("V_ASSIGN_USER_ID"));
            claimHandlerDto.setAssignStatus(rs.getString("V_ASSIGN_STATUS"));
            claimHandlerDto.setAssignDateTime(rs.getString("D_ASSIGN_DATE_TIME"));
            claimHandlerDto.setInitLiabilityAssignUserId(rs.getString("V_INIT_LIABILITY_ASSIGN_USER_ID"));
            claimHandlerDto.setInitLiabilityAssignDateTime(Utility.getDate(rs.getString("V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
            claimHandlerDto.setInitLiabilityAprvUserId(rs.getString("V_INIT_LIABILITY_APRV_USER_ID"));
            claimHandlerDto.setInitLiabilityAprvStatus(rs.getString("V_INIT_LIABILITY_APRV_STATUS"));
            claimHandlerDto.setInitLiabilityAprvDateTime(Utility.getDate(rs.getString("D_INIT_LIABILITY_APRV_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
            claimHandlerDto.setLiabilityAprvAssignUser(rs.getString("V_LIABILITY_APRV_ASSIGN_USER"));
            claimHandlerDto.setLiabilityAprvAssignDateTime(rs.getString("D_LIABILITY_APRV_ASSIGN_DATE_TIME"));
            claimHandlerDto.setLiabilityAprvUser(rs.getString("V_LIABILITY_APRV_USER"));
            claimHandlerDto.setLiabilityAprvStatus(rs.getString("V_LIABILITY_APRV_STATUS"));
            claimHandlerDto.setLiabilityAprvDateTime(rs.getString("D_LIABILITY_APRV_DATE_TIME"));
            claimHandlerDto.setManagerUserId(rs.getString("V_MANAGER_USER_ID"));
            claimHandlerDto.setChannelCode(rs.getString("V_CHANNEL_CODE"));
            claimHandlerDto.setAccessUserType(rs.getInt("N_ACCESS_USER_TYPE"));
            claimHandlerDto.setClaimStatus(rs.getInt("N_CLAIM_STATUS"));
            claimHandlerDto.setReserveAmount(rs.getBigDecimal("N_RESERVE_AMOUNT") == null ? BigDecimal.ZERO
                    : rs.getBigDecimal("N_RESERVE_AMOUNT"));
            claimHandlerDto.setReserveAmountAfterAprv(rs.getBigDecimal("N_RESERVE_AMOUNT_AFTER_APRV") == null ? BigDecimal.ZERO
                    : rs.getBigDecimal("N_RESERVE_AMOUNT_AFTER_APRV"));
            claimHandlerDto.setAprvTotAcrAmount(rs.getBigDecimal("N_APRV_TOT_ACR_AMOUNT") == null ? BigDecimal.ZERO
                    : rs.getBigDecimal("N_APRV_TOT_ACR_AMOUNT"));
            claimHandlerDto.setEngineerApprovedAmount(rs.getBigDecimal("N_ENGINEER_APRV_AMOUNT") == null ? BigDecimal.ZERO
                    : rs.getBigDecimal("N_ENGINEER_APRV_AMOUNT"));
            claimHandlerDto.setLossType(rs.getInt("N_LOSS_TYPE"));
            claimHandlerDto.setIsAllDocUpload(rs.getString("V_IS_ALL_DOC_UPLOAD"));
            claimHandlerDto.setIsCheckAllMndDocs(rs.getString("V_IS_CHECK_ALL_MND_DOCS"));
            claimHandlerDto.setPenaltyBaldTyre(rs.getBigDecimal("N_PENALTY_BALD_TYRE") == null ? BigDecimal.ZERO
                    : rs.getBigDecimal("N_PENALTY_BALD_TYRE"));
            claimHandlerDto.setPenaltyBaldTyreRate(rs.getBigDecimal("N_PENALTY_BALD_TYRE_RATE") == null ? BigDecimal.ZERO
                    : rs.getBigDecimal("N_PENALTY_BALD_TYRE_RATE"));
            claimHandlerDto.setPenaltyBaldTyreUser(rs.getString("V_PENALTY_BALD_TYRE_USER"));
            claimHandlerDto.setPenaltyBaldTyreDateTime(rs.getString("D_PENALTY_BALD_TYRE_DATE_TIME"));
            claimHandlerDto.setPenaltyUnderInsurce(rs.getBigDecimal("N_PENALTY_UNDER_INSURCE") == null ? BigDecimal.ZERO
                    : rs.getBigDecimal("N_PENALTY_UNDER_INSURCE"));
            claimHandlerDto.setPenaltyUnderInsurceRate(rs.getBigDecimal("N_PENALTY_UNDER_INSURCE_RATE") == null ? BigDecimal.ZERO
                    : rs.getBigDecimal("N_PENALTY_UNDER_INSURCE_RATE"));
            claimHandlerDto.setPenaltyUnderInsurceUser(rs.getString("V_PENALTY_UNDER_INSURCE_USER"));
            claimHandlerDto.setPenaltyUnderInsurceDateTime(rs.getString("D_PENALTY_UNDER_INSURCE_DATE_TIME"));
            claimHandlerDto.setIntimationChk(rs.getString("V_INTIMATION_CHK"));
            claimHandlerDto.setIntimationChkUser(rs.getString("V_INTIMATION_CHK_USER"));
            claimHandlerDto.setIntimationChkDateTime(rs.getString("D_INTIMATION_CHK_DATE_TIME"));
            claimHandlerDto.setIsLcChk1(rs.getString("V_IS_LC_CHK1"));
            claimHandlerDto.setIsLcChk2(rs.getString("V_IS_LC_CHK2"));
            claimHandlerDto.setIsLcChk3(rs.getString("V_IS_LC_CHK3"));
            claimHandlerDto.setIsLcChk4(rs.getString("V_IS_LC_CHK4"));
            claimHandlerDto.setIsLcChk5(rs.getString("V_IS_LC_CHK5"));
            claimHandlerDto.setIsLcChk6(rs.getString("V_IS_LC_CHK6"));
            claimHandlerDto.setIsLcChk7(rs.getString("V_IS_LC_CHK7"));
            claimHandlerDto.setIsLcChk8(rs.getString("V_IS_LC_CHK8"));
            claimHandlerDto.setLcChkUser(rs.getString("V_LC_CHK_USER"));
            claimHandlerDto.setLcChkDateTime(rs.getString("D_LC_CHK_DATE_TIME"));
            claimHandlerDto.setClaimPanelAssignUsers(rs.getString("V_CLAIM_PANEL_ASSIGN_USERS"));
            claimHandlerDto.setClaimPanelAssignUserDateTime(rs.getString("D_CLAIM_PANEL_ASSIGN_USER_DATE_TIME"));
            claimHandlerDto.setDecisionApproveClaimPanel(rs.getInt("N_DECISION_APPRV_CLAIM_PANEL"));
            claimHandlerDto.setClaimPanelDecision(rs.getString("V_CLAIM_PANEL_DECISION"));
            claimHandlerDto.setClaimPanelDecisionDateTime(rs.getString("D_CLAIM_PANEL_DECISION_DATE_TIME"));
            claimHandlerDto.setRepudiatedType(rs.getInt("N_REPUDIATED_TYPE"));
            claimHandlerDto.setIsPrintRepudiatedLetter(rs.getString("V_IS_PRINT_REPUDIATED_LETTER"));
            claimHandlerDto.setRepudiatedLetterPrintUserId(rs.getString("V_REPUDIATED_LETTER_PRINT_USER_ID"));
            claimHandlerDto.setRepudiatedLetterPrintDateTime(rs.getString("V_REPUDIATED_LETTER_PRINT_DATE_TIME"));
            claimHandlerDto.setFinancialInterest(rs.getString("V_FINANCIAL_INTEREST"));
            claimHandlerDto.setLeasingRefNo(rs.getInt("N_LEASING_REF_NO"));
            claimHandlerDto.setFinalizeUserId(rs.getString("V_FINALIZE_USER_ID"));
            claimHandlerDto.setFinalizeDateTime(rs.getString("D_FINALIZE_DATE_TIME"));
            claimHandlerDto.setIsGenarateSupplyOrder(rs.getString("V_IS_GENARATE_SUPPLY_ORDER"));
            claimHandlerDto.setSupplyOrderAssignUser(rs.getString("V_SUPPLY_ORDER_ASSIGN_USER"));
            claimHandlerDto.setSupplyOrderAssignDateTime(rs.getString("D_SUPPLY_ORDER_ASSIGN_DATE_TIME"));
            claimHandlerDto.setSupplyOrderCreateUser(rs.getString("V_SUPPLY_ORDER_CREATE_USER"));
            claimHandlerDto.setSupplyOrderCreateDateTime(rs.getString("D_SUPPLY_ORDER_CREATE_DATE_TIME"));
            claimHandlerDto.setSupplyOrderCreateClose(rs.getString("V_SUPPLY_ORDER_CREATE_CLOSE"));
            claimHandlerDto.setIsFileStore(rs.getString("V_IS_FILE_STORE"));
            claimHandlerDto.setFileUserStoreUserId(rs.getString("V_FILE_USER_STORE_USER_ID"));
            claimHandlerDto.setFileStoreDateTime(rs.getString("D_FILE_STORE_DATE_TIME"));
            claimHandlerDto.setReopenAssignUserId(rs.getString("V_REOPEN_ASSIGN_USER_ID"));
            claimHandlerDto.setReopenAssignUserDateTime(rs.getString("D_REOPEN_ASSIGN_USER_DATE_TIME"));
            claimHandlerDto.setReopenUserId(rs.getString("V_REOPEN_USER_ID"));
            claimHandlerDto.setReopenDateTime(rs.getString("D_REOPEN_DATE_TIME"));
            claimHandlerDto.setReopenDateTime(rs.getString("N_REOPEN_NO_OF_TIME"));
            claimHandlerDto.setIsGenFinalRemindLetter(rs.getString("V_IS_GEN_FINAL_REMIND_LETTER"));
            claimHandlerDto.setGenFinalRemindLetterDateTime(rs.getString("D_GEN_FINAL_REMIND_LETTER_DATE_TIME"));
            claimHandlerDto.setDecisionMakingAssignUserId(rs.getString("V_DECISION_MAKING_ASSIGN_USER_ID"));
            claimHandlerDto.setDecisionMakingAssignDateTime(rs.getString("D_DECISION_MAKING_ASSIGN_DATE_TIME"));
            claimHandlerDto.setInvestigationStatus(rs.getString("V_INVESTIGATION_STATUS"));
            claimHandlerDto.setInvestigationAssignUserId(rs.getString("V_INVESTIGATION_ASSIGN_USER_ID"));
            claimHandlerDto.setInvestigationAssignDateTime(rs.getString("D_INVESTIGATION_ASSIGN_DATE_TIME"));
            claimHandlerDto.setInvestigationArrangeUserId(rs.getString("V_INVESTIGATION_ARRANGE_USER_ID"));
            claimHandlerDto.setInvestigationArrangeDateTime(rs.getString("D_INVESTIGATION_ARRANGE_DATE_TIME"));
            claimHandlerDto.setInvestigationCompletedUserId(rs.getString("V_INVESTIGATION_COMPLETED_USER_ID"));
            claimHandlerDto.setInvestigationCompletedDateTime(rs.getString("D_INVESTIGATION_COMPLETED_DATE_TIME"));
            claimHandlerDto.setSpecialApprovalInputUserId(rs.getString("V_SPECIAL_APPROVAL_INPUT_USER_ID"));
            claimHandlerDto.setSpecialApprovalInputDateTime(rs.getString("V_SPECIAL_APPROVAL_INPUT_DATE_TIME"));
            claimHandlerDto.setSpecialApprovalUserId(rs.getString("V_SPECIAL_APPROVAL_USER_ID"));
            claimHandlerDto.setSpecialApprovalDateTime(rs.getString("V_SPECIAL_APPROVAL_DATE_TIME"));
            claimHandlerDto.setOldClaimStatus(rs.getInt("N_OLD_CLAIM_STATUS"));
            claimHandlerDto.setOldEngineerClaimStatus(rs.getInt("N_OLD_ENGINEER_CLAIM_STATUS"));

            claimHandlerDto.setIsDoubt(rs.getString("V_IS_DOUBT"));
            claimHandlerDto.setIsOnSiteOffer(rs.getString("V_IS_ON_SITE_OFFER"));
            claimHandlerDto.setAprvAdvanceAmount(rs.getBigDecimal("N_APRV_ADVANCE_AMOUNT") == null ? BigDecimal.ZERO
                    : rs.getBigDecimal("N_APRV_ADVANCE_AMOUNT"));
            claimHandlerDto.setReOpenType(rs.getString("V_REOPEN_TYPE"));
            claimHandlerDto.setCloseStatus(rs.getString("V_CLOSE_STATUS"));
            claimHandlerDto.setCloseUser(rs.getString("V_CLOSE_USER"));
            claimHandlerDto.setCloseDateTime(rs.getString("D_CLOSE_DATE_TIME"));
            claimHandlerDto.setRejectionLatterType(rs.getInt("N_REPUDIATED_LETTER_TYPE"));


            claimHandlerDto.setSupplyOrderAssignStatus(rs.getString("V_SUPPLY_ORDER_ASSIGN_STATUS"));
            claimHandlerDto.setInpStatus(rs.getString("V_INP_STATUS"));
            claimHandlerDto.setInpUserId(rs.getString("V_INP_USER_ID"));
            claimHandlerDto.setInpDateTime(rs.getString("D_INP_DATE_TIME"));
            claimHandlerDto.setVersionNo(rs.getInt("N_VERSION_NO"));
            claimHandlerDto.setLabourCost(rs.getBigDecimal("N_LABOUR_COST") == null ? BigDecimal.ZERO
                    : rs.getBigDecimal("N_LABOUR_COST"));
            claimHandlerDto.setPartCost(rs.getBigDecimal("N_PART_COST") == null ? BigDecimal.ZERO
                    : rs.getBigDecimal("N_PART_COST"));
            claimHandlerDto.setIsExcessInclude(null == rs.getString("V_IS_EXCESS_INCLUDE") ? AppConstant.NO : rs.getString("V_IS_EXCESS_INCLUDE"));
            claimHandlerDto.setIsProvideOffer(null == rs.getString("V_IS_PROVIDE_OFFER") ? AppConstant.NO : rs.getString("V_IS_PROVIDE_OFFER"));
            claimHandlerDto.setLetterPanelUserId(rs.getString("V_LETTER_PANEL_USER_ID"));
            claimHandlerDto.setAdvanceApprovalAssignUser(null == rs.getString("V_ADVANCE_APPROVAL_ASSIGN_USER") ? AppConstant.STRING_EMPTY : rs.getString("V_ADVANCE_APPROVAL_ASSIGN_USER"));
            claimHandlerDto.setAdvanceApprovalAssignDateTime(null == rs.getString("D_ADVANCE_APPROVAL_ASSIGN_DATE_TIME") ? AppConstant.STRING_EMPTY : rs.getString("D_ADVANCE_APPROVAL_ASSIGN_DATE_TIME"));
            claimHandlerDto.setAdvanceStatus(null == rs.getString("V_ADVANCE_STATUS") ? AppConstant.STRING_EMPTY : rs.getString("V_ADVANCE_STATUS"));
            claimHandlerDto.setAdvanceForwardedUser(null == rs.getString("V_ADVANCE_FORWARDED_USER") ? AppConstant.STRING_EMPTY : rs.getString("V_ADVANCE_FORWARDED_USER"));
            claimHandlerDto.setPendingAdvance(null == rs.getBigDecimal("N_APPROVAL_PENDING_ADVANCE_AMOUNT") ? BigDecimal.ZERO : rs.getBigDecimal("N_APPROVAL_PENDING_ADVANCE_AMOUNT"));
            claimHandlerDto.setAdvanceApprovedUser(null == rs.getString("V_ADVANCE_APPROVED_USER") ? AppConstant.STRING_EMPTY : rs.getString("V_ADVANCE_APPROVED_USER"));
            claimHandlerDto.setAdvanceApprovedDateTime(null == rs.getString("D_ADVANCE_APPROVED_DATE_TIME") ? AppConstant.STRING_EMPTY : rs.getString("D_ADVANCE_APPROVED_DATE_TIME"));
            claimHandlerDto.setForwardedEngineer(null == rs.getString("V_FORWARDED_ENGINEER") ? AppConstant.STRING_EMPTY : rs.getString("V_FORWARDED_ENGINEER"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimHandlerDto;
    }

    @Override
    public List<String> getPanelUserIdList(Connection connection, int type) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<String> userIdList = new ArrayList<>();
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_SELECT_PANEL_USER_IDS);
            ps.setInt(++index, type);
            rs = ps.executeQuery();
            while (rs.next()) {
                userIdList.add(rs.getString("V_USER_ID"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
        }
        return userIdList;
    }

    @Override
    public List<ClaimClaimPanelUserDto> searchByUserId(Connection connection, String userId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ClaimClaimPanelUserDto> claimClaimPanelUserDtos = new ArrayList<>();
        int index = 0;
        try {
            ps = connection.prepareStatement("SELECT N_ID, V_USER_ID, N_PANEL_ID, V_USER_STATUS, V_INPUT_USER, D_INPUT_DATETIME\n"
                    + "	FROM claim_claim_panel_user WHERE V_USER_ID = ?");
            ps.setString(++index, userId);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimClaimPanelUserDto claimClaimPanelUserDto = new ClaimClaimPanelUserDto();
                claimClaimPanelUserDto.setId(rs.getInt("N_ID"));
                claimClaimPanelUserDto.setInputDateTime(rs.getString("D_INPUT_DATETIME"));
                claimClaimPanelUserDto.setInputUser(rs.getString("V_INPUT_USER"));
                claimClaimPanelUserDto.setUserId(rs.getString("V_USER_ID"));
                claimClaimPanelUserDto.setUserPanelId(rs.getInt("N_PANEL_ID"));
                claimClaimPanelUserDto.setUserStatus(rs.getString("V_USER_STATUS"));
                claimClaimPanelUserDtos.add(claimClaimPanelUserDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return claimClaimPanelUserDtos;
    }

    @Override
    public void updateRejectionUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement("UPDATE claim_assign_claim_handler\n"
                    + "	SET\n"
                    + "		N_REPUDIATED_TYPE=?,\n"
                    + "		V_IS_PRINT_REPUDIATED_LETTER=?,\n"
                    + "		V_REPUDIATED_LETTER_PRINT_USER_ID=?,\n"
                    + "		V_REPUDIATED_LETTER_PRINT_DATE_TIME=?,\n"
                    + "N_REPUDIATED_LETTER_TYPE =?\n"
                    + "	WHERE N_CLAIM_NO = ?");
            ps.setInt(++index, claimHandlerDto.getRepudiatedType());
            ps.setString(++index, claimHandlerDto.getIsPrintRepudiatedLetter());
            ps.setString(++index, claimHandlerDto.getRepudiatedLetterPrintUserId());
            ps.setString(++index, claimHandlerDto.getRepudiatedLetterPrintDateTime());
            ps.setInt(++index, claimHandlerDto.getRejectionLatterType());
            ps.setInt(++index, claimHandlerDto.getClaimNo());

            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public List<ClaimRepudiatedReasonDto> getRepudiatedList(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ClaimRepudiatedReasonDto> claimRepudiatedReasonDtos = new ArrayList<>();
        int index = 0;
        try {
            ps = connection.prepareStatement("SELECT * FROM claim_repudiated_reason");
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimRepudiatedReasonDto claimRepudiatedReasonDto = new ClaimRepudiatedReasonDto();
                claimRepudiatedReasonDto.setRefNo(rs.getInt("N_ID"));
                claimRepudiatedReasonDto.setRepudiatedReasonCode(rs.getString("V_REPUDIATED_REASON_CODE"));
                claimRepudiatedReasonDto.setRepudiatedReasonDesc(rs.getString("V_REPUDIATED_REASON_DESC"));
                claimRepudiatedReasonDtos.add(claimRepudiatedReasonDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return claimRepudiatedReasonDtos;
    }

    @Override
    public boolean updateReserveAmount(BigDecimal reseveAcr, BigDecimal totAcr, Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATED_CLAIM_HANDLER_AMOUNT_BY_CLAIM_NO);
            ps.setBigDecimal(1, reseveAcr);
            ps.setBigDecimal(2, totAcr);
            ps.setInt(3, claimNo);

            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public void updateReserveAmountFromRTE(BigDecimal reseveAmt, BigDecimal reseveAmtAfterApr, BigDecimal totAmt, String aprvUser, Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATED_RESEVE_AMOUNT_FROM_RTE_BY_CLAIM_NO);
            ps.setBigDecimal(1, reseveAmt);
            ps.setBigDecimal(2, reseveAmtAfterApr);
            ps.setBigDecimal(3, totAmt);
            ps.setString(4, aprvUser);
            ps.setInt(5, claimNo);

            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updateReserveAcrAmount(BigDecimal reseveAmt, BigDecimal reseveAmtAfterApr, Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATED_RESEVE_AMOUNT_CLAIM_NO);
            ps.setBigDecimal(1, reseveAmt);
            ps.setBigDecimal(2, reseveAmtAfterApr);
            ps.setInt(3, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public void updateReserveAcrAmountAfetrApproved(BigDecimal reseveAmtAfterApr, Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_APPROVED_UPDATED_RESEVE_AMOUNT_CLAIM_NO);
            ps.setBigDecimal(1, reseveAmtAfterApr);
            ps.setInt(2, claimNo);

            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }


    @Override
    public void updateLossType(Connection connection, int claimNo, String type) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_LOSS_TYPE);
            ps.setString(++index, type);
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public boolean updateTotAcrAmount(Connection connection, Integer claimNo, BigDecimal amount) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_ACR_VALUE);
            ps.setBigDecimal(++index, amount);
            ps.setInt(++index, claimNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public boolean updatePenaltyAmount(Connection connection, Integer claimNo, BigDecimal amount, BigDecimal penaltyAmount, boolean isBold) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(isBold ? UPDATE_UNDER_PENALTITY_VALUE : UPDATE_PENALTITY_VALUE);
            ps.setBigDecimal(++index, amount);
            ps.setBigDecimal(++index, penaltyAmount);
            ps.setInt(++index, claimNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public List<String> getUserListByAccessUserType(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<String> userList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_USER_LIST);
            rs = ps.executeQuery();

            while (rs.next()) {
                userList.add(rs.getString("v_usrid"));
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return userList;
    }

    @Override
    public List<String> searchAllUserByInAccessUserType(Connection connection, String accessTypeList) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<String> userList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_USER_LIST_BY_IN_ACCESS_TYPE.concat(" IN(")
                    .concat(accessTypeList)
                    .concat(")")
                    .concat(" ORDER BY v_usrid"));
            rs = ps.executeQuery();
            while (rs.next()) {
                userList.add(rs.getString("v_usrid"));
            }
            rs.close();
            ps.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return userList;
    }
    @Override
    public List<String> searchAllUserByInAccessUserType(Connection connection, String accessTypeList, UserDto sessionUser) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<String> userList = new ArrayList<>();

        try {
            StringBuilder sql = new StringBuilder(SQL_SELECT_USER_LIST_BY_IN_ACCESS_TYPE)
                    .append(" IN(")
                    .append(accessTypeList)
                    .append(")");

            // Conditionally add team filter
            if (sessionUser != null && sessionUser.getAccessUserType() == 49) {
                sql.append(" AND N_TEAM_ID = ").append(sessionUser.getTeamId());
            }

            sql.append(" ORDER BY v_usrid");

            ps = connection.prepareStatement(sql.toString());
            rs = ps.executeQuery();

            while (rs.next()) {
                userList.add(rs.getString("v_usrid"));
            }

            rs.close();
            ps.close();

        } catch (Exception e) {
            LOGGER.error("Error retrieving users", e);
        }

        return userList;
    }

    @Override
    public boolean updateSpecialComment(Connection connection, Integer claimNo, String userId, String inputUserId) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_SPECIAL_COMMENT_BY_CLAIM_NO);
            ps.setString(++index, userId);
            ps.setString(++index, Utility.sysDateTime());
            ps.setString(++index, inputUserId);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, claimNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public boolean updateSupplierOrderAssignDetails(Connection connection, Integer claimNo, String userId, String status, String assignDateTime) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(null == userId ? UPDATE_FORWARD_SUPPLIER_ORDER : UPDATE_FORWARD_SUPPLIER_ORDER_WITH_USER);
            if (null != userId) {
                ps.setString(++index, userId);
            }
            ps.setString(++index, status);
            ps.setString(++index, assignDateTime);
            ps.setInt(++index, claimNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public boolean updateSupplierOrderAssignStatus(Connection connection, Integer claimNo, String status) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_FORWARD_SUPPLIER_ORDER_STATUS);
            ps.setString(++index, status);
            ps.setInt(++index, claimNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public DataGridDto getClaimHandlerScrutinizingDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String[] orderFields = {orderField};
        Object[] instances = {AppConstant.PRIORITY_HIGH, AppConstant.PRIORITY_NORMAL};
        final String SQL_ORDER = orderByCases(start, length, orderType, "t1.V_PRIORITY", orderFields, instances).toString();

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.D_ACCID_DATE BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SEL_SQL = SQL_SELECT_SCRUTINIZING_LIST.concat(SQL_SEARCH).concat(SQL_ORDER);

        final String COUNT_SQL = SQL_SELECT_SCRUTINIZING_COUNT.concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    if (5 == type) {
                        if (isPendingInspectionFound(conn, rs.getInt("t1.N_CLIM_NO"))) {
                            --count;
                            continue;
                        }
                    } else if (60 == type) {
                        if (!isPendingInspectionFound(conn, rs.getInt("t1.N_CLIM_NO"))) {
                            --count;
                            continue;
                        }
                    }
                    ClaimHandlerGridDto claimHandlerDto = new ClaimHandlerGridDto();
                    claimHandlerDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    claimHandlerDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    claimHandlerDto.setPolicyNumberValue(rs.getString("t1.V_POL_NUMBER"));
                    claimHandlerDto.setInspectionStatus(0);
                    claimHandlerDto.setAccidentDate(rs.getString("t1.D_ACCID_DATE"));
                    claimHandlerDto.setPriority(null == rs.getString("t1.V_PRIORITY") || rs.getString("t1.V_PRIORITY").isEmpty() ? AppConstant.PRIORITY_NORMAL : rs.getString("t1.V_PRIORITY"));
                    claimHandlerDto.setDocumentStatus(null == rs.getString("t2.V_IS_ALL_DOC_UPLOAD") ? AppConstant.NO : rs.getString("t2.V_IS_ALL_DOC_UPLOAD"));
                    claimHandlerDto.setDocumentCheck(null == rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS") ? AppConstant.NO : rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS"));
                    claimHandlerDto.setLiabilityStatus(null == rs.getString("t2.V_LIABILITY_APRV_STATUS") ? AppConstant.NO : rs.getString("t2.V_LIABILITY_APRV_STATUS"));
                    claimHandlerDto.setClaimStatus(rs.getInt("t2.N_CLAIM_STATUS"));
                    claimHandlerDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                    claimHandlerDto.setTxnId(rs.getInt("t2.N_TXN_NO"));
                    claimHandlerDto.setAcr(rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT").toString());
                    claimHandlerDto.setPresentReverseAmount(rs.getBigDecimal("t2.N_RESERVE_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_RESERVE_AMOUNT").toString());
                    claimHandlerDto.setFileStore(rs.getString("t2.V_IS_FILE_STORE"));
                    claimHandlerDto.setPartialLoss(rs.getInt("t2.N_LOSS_TYPE"));
                    claimHandlerDto.setLiabilityAssignUser(rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER"));
                    claimHandlerDto.setLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT).equals(AppConstant.DEFAULT_DATE)
                            ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT));

                    claimHandlerDto.setIntLiabilityAssignUser(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID"));

                    claimHandlerDto.setIntLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                    claimHandlerDto.setInvestigationStatus(rs.getString("t2.V_INVESTIGATION_STATUS"));
                    claimHandlerDto.setCloseStatus(rs.getString("t2.V_CLOSE_STATUS"));
                    claimHandlerDto.setPolicyChannelType(null == rs.getString("t1.V_POLICY_CHANNEL_TYPE") || rs.getString("t1.V_POLICY_CHANNEL_TYPE").trim().isEmpty() ? PolicyChannelType.CONVENTIONAL.name() : rs.getString("t1.V_POLICY_CHANNEL_TYPE"));

                    if (null != rs.getString("t2.V_CLOSE_STATUS") && rs.getString("t2.V_CLOSE_STATUS").equals("CLOSE")) {
                        claimHandlerDto.setFinalizeStatus(AppConstant.YES);
                    } else {
                        claimHandlerDto.setFinalizeStatus(AppConstant.NO);
                    }
                    claimHandlerDto.setIndex(++index);
                    handlerList.add(claimHandlerDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public boolean updateLabourCostAndPartCostAmount(Connection connection, Integer claimNo, BigDecimal labourCost, BigDecimal partCost) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_LABOUR_COST_AND_PART_COST_VALUE);
            ps.setBigDecimal(++index, labourCost);
            ps.setBigDecimal(++index, partCost);
            ps.setInt(++index, claimNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public boolean updateSwapClaimStatus(Connection connection, Integer claimNo) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_SWAP_CLAIM_STATUS_BY_CLAIM_NO)) {
            ps.setInt(++index, claimNo);
            if (ps.executeUpdate() > 0) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return false;


    }

    @Override
    public boolean isLiablityChecked(Connection connection, Integer claimNo) {
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SQL_SELECT_IS_LIABLITY_CHECKED);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                if (null != rs.getString("V_LC_CHK_USER") && !rs.getString("V_LC_CHK_USER").isEmpty()) {
                    return true;
                }
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public void updateAdvanceAmount(Connection connection, Integer claimNo, BigDecimal amount) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_APRV_ADVANCE_AMOUNT);
            ps.setBigDecimal(++index, amount);
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateDoubtStatus(Connection connection, Integer claimNo, String doubtStatus) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_IS_DOUBT);
            ps.setString(++index, doubtStatus);
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateClaimClose(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_TO_CLOSE);
            ps.setString(++index, claimHandlerDto.getCloseStatus());
            ps.setString(++index, claimHandlerDto.getCloseUser());
            ps.setString(++index, claimHandlerDto.getCloseDateTime());
            ps.setInt(++index, claimHandlerDto.getClaimNo());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateClaimReopen(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_TO_REOPEN);
            ps.setString(++index, claimHandlerDto.getCloseStatus());
            ps.setString(++index, claimHandlerDto.getReOpenType());
            ps.setString(++index, claimHandlerDto.getReopenUserId());
            ps.setString(++index, claimHandlerDto.getReopenDateTime());
            ps.setInt(++index, claimHandlerDto.getClaimNo());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateOnsiteOffer(Connection connection, int claimNo, int offerType) throws Exception {
        PreparedStatement ps = null;
        String type;
        try {
            if (offerType == 1) {
                type = "Y";
            } else {
                type = "N";
            }
            ps = connection.prepareStatement(UPDATE_ONSITE_OFFER_TYPE);
            ps.setString(1, type);
            ps.setInt(2, claimNo);

            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                try {
                    ps.close();
                } catch (SQLException e) {
                    LOGGER.error(e.getMessage());
                }
            }
        }
    }

    @Override
    public List<UserDto> getBranchUserList(Connection connection, String empNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<UserDto> userList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_USERS_BY_EMP_NO);
            ps.setString(1, empNo);
            rs = ps.executeQuery();

            while (rs.next()) {
                UserDto user = new UserDto();
                user.setUserId(rs.getString("v_usrid"));
                userList.add(user);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return userList;
    }

    @Override
    public void updateClaimStatusFileStatus(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_STATUS_FILE_STATUS);
            ps.setString(++index, claimHandlerDto.getIsFileStore());
            ps.setInt(++index, claimHandlerDto.getClaimStatus());
            ps.setInt(++index, claimHandlerDto.getClaimNo());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public Map<Integer, Integer> getUploadedDocumentsType(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        Map<Integer, Integer> map = new HashMap<>();
        try {
            ps = connection.prepareStatement(SELECT_UPLOADED_DOCUMENTS);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            while (rs.next()) {
                Integer docType = rs.getInt("t1.N_DOC_TYPE_ID");
                map.put(docType, docType);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return map;
    }

    @Override
    public boolean isAvailableOnsiteOrOffsite(Connection connection, int claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_IS_AVAILABLE_ONSITE_OFFSITE);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                Integer claim_no = rs.getInt("claim_no");
                if (claim_no != null) {
                    return true;
                }
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public void updateClaimHandlerReopen(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_STATUS_FILE_REOPEN);
            ps.setString(++index, claimHandlerDto.getAssignUserId());
            ps.setString(++index, claimHandlerDto.getAssignStatus());
            ps.setString(++index, claimHandlerDto.getAssignDateTime());
            ps.setString(++index, claimHandlerDto.getLiabilityAprvAssignUser());
            ps.setString(++index, claimHandlerDto.getLiabilityAprvStatus());
            ps.setString(++index, claimHandlerDto.getLiabilityAprvAssignDateTime());
            ps.setInt(++index, claimHandlerDto.getClaimNo());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public boolean updateClaimAssignUserByrefNo(Connection connection, Integer refNo, String assignUser) throws MisynJDBCException {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_ASSIGN_USER_BY_INSPECTION_ID);
            ps.setString(++index, assignUser);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, refNo);

            if (ps.executeUpdate() > 0) {
                return true;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return false;
    }

    @Override
    public boolean isUserAlreadyAssigned(Connection connection, Integer refNo, String assignUser) throws MisynJDBCException {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_ASSIGNED_USER_BY_INSPECTION_ID);
            ps.setInt(1, refNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                String currentAssignUser = rs.getString("v_assign_rte_user");
                return assignUser != null && assignUser.equals(currentAssignUser);
            }
            return false;  // No record found, so no assignment
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new MisynJDBCException("System Error", e);
        } finally {
            try {
                if (rs != null) rs.close();
                if (ps != null) ps.close();
            } catch (Exception ex) {
                LOGGER.warn("Failed to close resources", ex);
            }
        }
    }
    @Override
    public List<ClaimsDto> getTheftClaimsList(Connection connection, String sysDate, Integer days) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ClaimsDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_THEFT_CLAIMS);
            ps.setString(1, sysDate);
            ps.setInt(2, days);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimsDto claimsDto = new ClaimsDto();
                claimsDto.setClaimNo(rs.getInt("N_CLIM_NO"));
                claimsDto.setAccidDate(rs.getString("D_ACCID_DATE"));
                claimsDto.setVehicleNo(rs.getString("V_VEHICLE_NO"));
                claimsDto.setCoverNoteNo(rs.getString("V_COVER_NOTE_NO"));
                claimsDto.getPolicyDto().setProduct(rs.getString("V_PRODUCT"));
                claimsDto.getPolicyDto().setCategoryDescription(rs.getString("V_CATEGORY_DESC"));
                list.add(claimsDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public BigDecimal getTotalAcrAmount(Connection connection, int claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        BigDecimal acr = null;
        try {
            ps = connection.prepareStatement(SELECT_APPROVE_TOTAL_ACR);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            if (rs.next()) {
                acr = rs.getBigDecimal("N_APRV_TOT_ACR_AMOUNT");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return acr;
    }

    @Override
    public BigDecimal getReserveAmountByClaimNo(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        BigDecimal reserveAmount = BigDecimal.ZERO;
        try {
            ps = connection.prepareStatement(SELECT_APPROVE_TOTAL_ACR);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            if (rs.next()) {
                reserveAmount = rs.getBigDecimal("N_RESERVE_AMOUNT");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
        }
        return reserveAmount;
    }

    @Override
    public String getMobileNoByAssignId(Connection connection, String assignId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String mobileNo = null;
        try {
            ps = connection.prepareStatement(SELECT_MOBILE_NO_BY_ASSIGN_USER_ID);
            ps.setString(1, assignId);
            rs = ps.executeQuery();

            if (rs.next()) {
                mobileNo = rs.getString("v_mobile");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return mobileNo;
    }

    @Override
    public boolean updateExcessIncludeStatus(Connection connection, String status, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(UPDATE_EXCESS_INCLUDE_BY_CLAIM_NO);
            ps.setString(1, status);
            ps.setInt(2, claimNo);
            return ps.executeUpdate() > 0;

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public void changeAssignUser(Connection connection, String assignUser, int claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(UPDATE_ASSIGN_USER_AND_ASSIGN_DATE);
            ps.setString(1, assignUser);
            ps.setString(2, Utility.sysDate(AppConstant.DATE_TIME_FORMAT));
            ps.setInt(3, claimNo);
            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    @Override
    public boolean updateInitialLiabilityUserLiabilityPending(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_INITIAL_LIABILITY_USER_LIABILITY_PENDING)) {
            ps.setString(++index, claimHandlerDto.getInitLiabilityAssignDateTime());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAssignUserId());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAprvStatus());
            ps.setInt(++index, claimHandlerDto.getClaimNo());

            if (ps.executeUpdate() > 0) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return false;
    }

    @Override
    public boolean isCheckInitLiabilityAssignUserChangeRequest(Connection connection, String user, int claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SQL_IS_INIT_LIABILITY_REQUEST);
            ps.setInt(1, claimNo);
            ps.setString(2, user);
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;

            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }

    @Override
    public ClaimHandlerDto updateAssignUserIfLiabilityApproved(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_ASSIGN_USER_ALREADY_LIABILITY_APPROVED)) {
            ps.setString(++index, claimHandlerDto.getAssignDateTime());
            ps.setString(++index, claimHandlerDto.getAssignUserId());
            ps.setString(++index, claimHandlerDto.getAssignStatus());
            ps.setInt(++index, claimHandlerDto.getClaimNo());

            if (ps.executeUpdate() > 0) {
                return claimHandlerDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public void updateReserveAmountAfterApproved(Connection connection, BigDecimal reserveAmountAfterApproved, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_RESERVE_AMOUNT_AFTER_APPROVED_BY_CLAIM_NO);
            ps.setBigDecimal(1, reserveAmountAfterApproved);
            ps.setInt(2, claimNo);

            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public BigDecimal getReserveAmountAfterApproved(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        BigDecimal reserveAmountAfterApproved = BigDecimal.ZERO;
        try {
            ps = connection.prepareStatement(SELECT_RESERVE_AMOUNT_AFTER_APPROVED_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            if (rs.next()) {
                reserveAmountAfterApproved = rs.getBigDecimal("N_RESERVE_AMOUNT_AFTER_APRV");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return reserveAmountAfterApproved;
    }

    @Override
    public BigDecimal getReserveAmount(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        BigDecimal reserveAmountAfterApproved = BigDecimal.ZERO;
        try {
            ps = connection.prepareStatement(SELECT_RESERVE_AMOUNT_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            if (rs.next()) {
                reserveAmountAfterApproved = rs.getBigDecimal("N_RESERVE_AMOUNT");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return reserveAmountAfterApproved;
    }

    @Override
    public void updateReserveAmountAndReserveAmountAfterApproved(Connection connection, BigDecimal reserveAmount, BigDecimal reserveAmountAfterApproved, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_RESERVE_AMOUNT_AND_AFTER_APPROVED);
            ps.setBigDecimal(1, reserveAmount);
            ps.setBigDecimal(2, reserveAmountAfterApproved);
            ps.setInt(3, claimNo);

            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public BigDecimal getAdvanceAmount(Connection connection, int claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        BigDecimal reserveAmountAfterApproved = BigDecimal.ZERO;
        try {
            ps = connection.prepareStatement(SELECT_APRV_ADVANCE_AMOUNT_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            if (rs.next()) {
                reserveAmountAfterApproved = rs.getBigDecimal("N_APRV_ADVANCE_AMOUNT");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return reserveAmountAfterApproved;
    }

    @Override
    public void updateIsProvideOffer(Connection connection, int claimNo, String isProvideOffer) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_V_IS_PROVIDE_OFFER_BY_N_CLAIM_NO);
            ps.setString(1, isProvideOffer);
            ps.setInt(2, claimNo);
            ps.executeUpdate();
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public void updateClaimAssignUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_CLAIM_ASSIGN_USER)) {
            ps.setString(++index, claimHandlerDto.getAssignDateTime());
            ps.setString(++index, claimHandlerDto.getAssignUserId());
            ps.setString(++index, claimHandlerDto.getAssignUserId());
            ps.setInt(++index, claimHandlerDto.getClaimNo());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updateInitialLiabilityUserAndAssignUser(Connection connection, ClaimHandlerDto claimHandlerDto) throws Exception {
        int index = 0;
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_INIT_LIABILITY_AND_ASSIGN_USER);
            ps.setString(++index, claimHandlerDto.getAssignDateTime());
            ps.setString(++index, claimHandlerDto.getAssignUserId());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAssignDateTime());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAssignUserId());
            ps.setString(++index, claimHandlerDto.getInitLiabilityAprvStatus());
            ps.setString(++index, claimHandlerDto.getAssignUserId());
            ps.setInt(++index, claimHandlerDto.getClaimNo());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public boolean isProvideOffer(Connection connection, Integer claimNo) {
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_IS_PROVIDE_OFFER_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                if (null != rs.getString("V_IS_PROVIDE_OFFER") && rs.getString("V_IS_PROVIDE_OFFER").equalsIgnoreCase(AppConstant.YES)) {
                    return true;
                }

            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public boolean isOnsiteOrOffSitePending(Connection connection, int claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_IS_AVAILABLE_PENDING_ONSITE_OR_OFFSITE);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                Integer claim_no = rs.getInt("claim_no");
                if (claim_no != null) {
                    return true;
                }
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public BigDecimal getEngineerApprovedAmount(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        BigDecimal approvedAmount = null;
        try {
            ps = connection.prepareStatement(GET_ENGINEER_APPROVED_AMOUNT_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                approvedAmount = rs.getBigDecimal("N_ENGINEER_APRV_AMOUNT");
            }
            return approvedAmount;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void updateEngineerApprovedAmount(Connection connection, Integer claimNo, BigDecimal payableAmount) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_ENGINEER_APPROVED_AMOUNT);
            ps.setBigDecimal(1, payableAmount);
            ps.setInt(2, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void updateLetterPanelUser(Connection connection, int claimNo, String user) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_LETTER_PANEL_USER);
            ps.setString(++index, user);
            ps.setString(++index,Utility.sysDateTime());
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateAdvanceRequest(Connection connection, Integer claimNo, UserDto assignUser, UserDto user, BigDecimal advanceAmount) throws Exception {
        PreparedStatement ps;
        String assignUserName;
        String advanceStatus = null;
        Integer claimStatus = null;
        try {
            if (null == assignUser) {
                assignUserName = AppConstant.STRING_EMPTY;
                advanceStatus = AdvanceRequestStatusEnum.REJECTED.getAdvanceRequestStatusEnum();
                claimStatus = AppConstant.CLAIM_STATUS_LIABILITY_APPROVED;
            } else {
                assignUserName = assignUser.getUserId();
                switch (user.getAccessUserType()) {
                    case 27:
                    case 28:
                        claimStatus = switch (assignUser.getAccessUserType()) {
                            case 27 -> {
                                advanceStatus = AdvanceRequestStatusEnum.FORWARD_TO_SPARE_PARTS_COORDINATOR.getAdvanceRequestStatusEnum();
                                yield AppConstant.FORWARD_TO_SPARE_PARTS_COORD_FOR_ADVANCE_APPROVAL;
                            }
                            case 28 -> {
                                advanceStatus = AdvanceRequestStatusEnum.FORWARD_TO_SCRUTINIZING_TEAM.getAdvanceRequestStatusEnum();
                                yield AppConstant.FORWARD_TO_BILL_CHECKING_TEAM_FOR_ADVANCE_APPROVAL;
                            }
                            default -> claimStatus;
                        };
                        break;
                    default:
                        advanceStatus = AdvanceRequestStatusEnum.PENDING.getAdvanceRequestStatusEnum();
                        if (assignUser.getAccessUserType() == AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR) {
                            claimStatus = AppConstant.FORWARD_TO_SPARE_PARTS_COORD_FOR_ADVANCE_APPROVAL;
                        } else {
                            claimStatus = AppConstant.FORWARD_TO_BILL_CHECKING_TEAM_FOR_ADVANCE_APPROVAL;
                        }

                }
            }
            ps = connection.prepareStatement(UPDATE_ADVANCE_REQUEST);
            ps.setString(1, assignUserName);
            ps.setString(2, null == assignUser ? Utility.getDate(AppConstant.DEFAULT_DATE_TIME, AppConstant.DATE_TIME_FORMAT) : Utility.sysDateTime());
            ps.setString(3, advanceStatus);
            ps.setString(4, user.getUserId());
            ps.setBigDecimal(5, advanceAmount);
            ps.setString(6, AppConstant.STRING_EMPTY);
            ps.setString(7, AppConstant.DEFAULT_DATE_TIME);
            ps.setInt(8, claimStatus);
            ps.setInt(9, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public String getAssignedClaimHandler(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet resultSet = null;
        try {
            ps = connection.prepareStatement(GET_ASSIGNED_USER);
            ps.setInt(1, claimNo);
            resultSet = ps.executeQuery();
            while (resultSet.next()) {
                return resultSet.getString("V_ASSIGN_USER_ID");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return null;
    }

    @Override
    public void approveAdvance(Connection connection, Integer claimNo, BigDecimal advance, UserDto sessionUser) throws Exception {
        PreparedStatement ps;
        String advanceStatus = null;
        try {
            advanceStatus = switch (sessionUser.getAccessUserType()) {
                case 27 -> AdvanceRequestStatusEnum.SPARE_PARTS_COORDINATOR_APPROVED.getAdvanceRequestStatusEnum();
                case 28 -> AdvanceRequestStatusEnum.SCRUTINIZING_TEAM_APPROVED.getAdvanceRequestStatusEnum();
                default -> advanceStatus;
            };
            ps = connection.prepareStatement(APPROVE_ADVANCE);
            ps.setBigDecimal(1, advance);
            ps.setString(2, sessionUser.getUserId());
            ps.setString(3, Utility.sysDateTime());
            ps.setString(4, advanceStatus);
            ps.setInt(5, AppConstant.CLAIM_STATUS_LIABILITY_APPROVED);
            ps.setInt(6, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void recallAdvance(Connection connection, Integer claimNo, UserDto user) throws Exception {
        PreparedStatement ps1;

        String userName;
        int claimStatus;
        String advanceStatus;
        try {
            userName = user.getUserId();
            switch (user.getAccessUserType()) {
                case 27:
                    claimStatus = AppConstant.FORWARD_TO_SPARE_PARTS_COORD_FOR_ADVANCE_APPROVAL;
                    advanceStatus = AdvanceRequestStatusEnum.PENDING.getAdvanceRequestStatusEnum();
                    break;
                case 28:
                    claimStatus = AppConstant.FORWARD_TO_BILL_CHECKING_TEAM_FOR_ADVANCE_APPROVAL;
                    advanceStatus = AdvanceRequestStatusEnum.PENDING.getAdvanceRequestStatusEnum();
                    break;
                default:
                    userName = AppConstant.STRING_EMPTY;
                    claimStatus = AppConstant.CLAIM_STATUS_LIABILITY_APPROVED;
                    advanceStatus = AdvanceRequestStatusEnum.REJECTED.getAdvanceRequestStatusEnum();
            }
            ps1 = connection.prepareStatement(RECALL_ADVANCE);
            ps1.setString(1, userName);
            ps1.setString(2, Utility.sysDateTime());
            ps1.setString(3, advanceStatus);
            ps1.setInt(4, claimStatus);
            ps1.setInt(5, claimNo);
            ps1.executeUpdate();

            ps1.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public DataGridDto getAdvanceForwardList(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String user) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List<Object> handlerList = new ArrayList<>(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String[] orderFields = {orderField};
        Object[] instances = {AppConstant.PRIORITY_HIGH, AppConstant.PRIORITY_NORMAL};
        final String SQL_ORDER = orderByCases(start, length, orderType, "t1.V_PRIORITY", orderFields, instances).toString();

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.D_ACCID_DATE BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SQL_GROUP = formatGroupSQL("N_CLIM_NO").toString();

        final String SEL_SQL = SQL_SELECT_ADVANCE_FORWARD_LIST.concat(SQL_SEARCH).concat(SQL_GROUP).concat(SQL_ORDER);
        final String COUNT_SQL = SQL_COUNT_ADVANCE_FORWARD_LIST.concat(SQL_SEARCH).concat(SQL_GROUP).concat(SQL_ORDER);

        try {
            ps = connection.prepareStatement(COUNT_SQL);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    count = rs.getInt("cnt");
                }
                ps.close();
            }
            ps = connection.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ClaimHandlerGridDto claimHandlerDto = new ClaimHandlerGridDto();
                    claimHandlerDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    claimHandlerDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    claimHandlerDto.setPolicyNumberValue(rs.getString("t1.V_POL_NUMBER"));
                    claimHandlerDto.setInspectionStatus(0);
                    claimHandlerDto.setAccidentDate(rs.getString("t1.D_ACCID_DATE"));
                    claimHandlerDto.setPriority(null == rs.getString("t1.V_PRIORITY") || rs.getString("t1.V_PRIORITY").isEmpty() ? AppConstant.PRIORITY_NORMAL : rs.getString("t1.V_PRIORITY"));
                    claimHandlerDto.setAssignDateTime(Utility.getDate(rs.getString("t2.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                    claimHandlerDto.setDocumentStatus(null == rs.getString("t2.V_IS_ALL_DOC_UPLOAD") ? AppConstant.NO : rs.getString("t2.V_IS_ALL_DOC_UPLOAD"));
                    claimHandlerDto.setDocumentCheck(null == rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS") ? AppConstant.NO : rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS"));
                    claimHandlerDto.setLiabilityStatus(null == rs.getString("t2.V_LIABILITY_APRV_STATUS") ? AppConstant.NO : rs.getString("t2.V_LIABILITY_APRV_STATUS"));
                    claimHandlerDto.setClaimStatus(rs.getInt("t2.N_CLAIM_STATUS"));
                    claimHandlerDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                    claimHandlerDto.setTxnId(rs.getInt("t2.N_TXN_NO"));
                    claimHandlerDto.setAcr(rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT").toString());
                    claimHandlerDto.setPresentReverseAmount(rs.getBigDecimal("t2.N_RESERVE_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_RESERVE_AMOUNT").toString());
                    claimHandlerDto.setFileStore(rs.getString("t2.V_IS_FILE_STORE"));
                    claimHandlerDto.setPartialLoss(rs.getInt("t2.N_LOSS_TYPE"));
                    claimHandlerDto.setAssignUser(rs.getString("t2.V_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_ASSIGN_USER_ID"));
                    claimHandlerDto.setLiabilityAssignUser(rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER"));
                    claimHandlerDto.setLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT).equals(AppConstant.DEFAULT_DATE)
                            ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT));

                    claimHandlerDto.setIntLiabilityAssignUser(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID") == null || rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID").isEmpty() ? AppConstant.STRING_EMPTY : rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID"));

                    claimHandlerDto.setIntLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                    claimHandlerDto.setInvestigationStatus(rs.getString("t2.V_INVESTIGATION_STATUS"));

                    claimHandlerDto.setIsDoubt(rs.getString("t2.V_IS_DOUBT"));
                    claimHandlerDto.setIsOnSiteOffer(rs.getString("t2.V_IS_ON_SITE_OFFER"));
                    claimHandlerDto.setAprvAdvanceAmount(rs.getBigDecimal("t2.N_APRV_ADVANCE_AMOUNT"));
                    claimHandlerDto.setReOpenType(rs.getString("t2.V_REOPEN_TYPE"));
                    claimHandlerDto.setCloseStatus(rs.getString("t2.V_CLOSE_STATUS"));
                    claimHandlerDto.setCloseUser(rs.getString("t2.V_CLOSE_USER"));
                    claimHandlerDto.setCloseDateTime(rs.getString("t2.D_CLOSE_DATE_TIME"));
                    claimHandlerDto.setPolicyChannelType(null == rs.getString("t1.V_POLICY_CHANNEL_TYPE") ? PolicyChannelType.CONVENTIONAL.name() : (rs.getString("t1.V_POLICY_CHANNEL_TYPE").equals("TAKAFULL") ? PolicyChannelType.TAKAFUL.name() : PolicyChannelType.CONVENTIONAL.name()));
                    if (null != rs.getString("t2.V_CLOSE_STATUS") && rs.getString("t2.V_CLOSE_STATUS").equals("CLOSE")) {
                        claimHandlerDto.setFinalizeStatus(AppConstant.YES);
                    } else {
                        claimHandlerDto.setFinalizeStatus(AppConstant.NO);
                    }

                    if (claimHandlerDto.getClaimStatus() == 43 || claimHandlerDto.getClaimStatus() == 46 || claimHandlerDto.getClaimStatus() == 48 || claimHandlerDto.getClaimStatus() == 52
                            || claimHandlerDto.getClaimStatus() == 53 || claimHandlerDto.getClaimStatus() == 54 || claimHandlerDto.getClaimStatus() == 55 || claimHandlerDto.getClaimStatus() == 56 || claimHandlerDto.getClaimStatus() == 68) {
                        claimHandlerDto.setClaimStatusDesc("Pending Decision");
                    }

                    claimHandlerDto.setIndex(++index);
                    handlerList.add(claimHandlerDto);
                    ++count;
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public String getAdvanceApprovalAssignUser(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        String userName = null;
        try {
            ps = connection.prepareStatement(SELECT_ADVANCE_ASSIGN_USER);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                userName = rs.getString("V_ADVANCE_APPROVAL_ASSIGN_USER");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return userName;
    }

    @Override
    public UserDto getAdvanceForwardedUser(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        UserDto user = null;
        try {
            ps = connection.prepareStatement(SELECT_ADVANCE_FORWARDED_USER);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                if (null != rs.getString("V_ADVANCE_FORWARDED_USER")) {
                    user = new UserDto();
                    user.setUserId(rs.getString("V_ADVANCE_FORWARDED_USER"));
                    user.setAccessUserType(rs.getInt("n_accessusrtype"));
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return user;
    }

    @Override
    public UserDto getAdvanceApprovedUser(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        UserDto user = null;
        try {
            ps = connection.prepareStatement(SELECT_ADVANCE_APPROVED_USER);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                if (null != rs.getString("V_ADVANCE_APPROVED_USER")) {
                    user = new UserDto();
                    user.setUserId(rs.getString("V_ADVANCE_APPROVED_USER"));
                    user.setAccessUserType(rs.getInt("n_accessusrtype"));
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return user;
    }

    @Override
    public boolean updateClaimApproveAssignUserByrefNo(Connection connection, Integer refNo, String assignUser, boolean rteApproved) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(rteApproved ? UPDATE_CLAIM_APPROVE_ASSIGN_USER_BY_INSPECTION_ID : UPDATE_PENDING_CLAIM_APPROVE_ASSIGN_USER_BY_INSPECTION_ID);
            ps.setString(++index, assignUser);
            ps.setString(++index, Utility.sysDateTime());
            if (!rteApproved) {
                ps.setInt(++index, ClaimStatus.INSPECTION_FORWARDED.getClaimStatus());
            }
            ps.setInt(++index, refNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean forwardToEngineer(Connection connection, Integer claimNo, String rteUser) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(FORWARD_TO_ENGINEER);
            ps.setInt(1, ClaimStatus.TASK_FORWARDED_TO_ENGINEER.getClaimStatus());
            ps.setString(2, rteUser);
            ps.setInt(3, claimNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public String getClaimForwardedEngineer(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        String assignUser = AppConstant.STRING_EMPTY;
        try {
            ps = connection.prepareStatement(GET_CLAIM_FORWARDED_ENGINEER);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                assignUser = null == rs.getString("V_FORWARDED_ENGINEER") ? AppConstant.STRING_EMPTY : rs.getString("V_FORWARDED_ENGINEER");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return assignUser;
    }

    @Override
    public boolean recallFromForwardedEngineer(Connection connection, Integer claimNo, Integer oldEngineerClaimStatus) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(RECALL_CLAIM_FROM_FORWARDED_ENGINEER_WHEN_SPECIAL_COMMENT);

            ps.setString(1, AppConstant.STRING_EMPTY);
            ps.setInt(2, claimNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public DataGridDto getClaimsForEngineerDataGrid(Connection connection, List<FieldParameterDto> parameterList, int drawRendomId, int start, int length, String columnOrder, String orderColumnName, String fromDate, String toDate, boolean equals) {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();

        String[] orderFields = {orderColumnName};
        Object[] instances = {AppConstant.PRIORITY_HIGH, AppConstant.PRIORITY_NORMAL};
        final String SQL_ORDER = orderByCases(start, length, columnOrder, "t1.V_PRIORITY", orderFields, instances).toString();

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.D_ACCID_DATE BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }
        final String SEL_SQL = equals ? SELECT_CLAIMS_FOR_ENGINEER.concat(SQL_SEARCH).concat(SQL_ORDER) : SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);

        final String COUNT_SQL = equals ? SQL_COUNT_CLAIMS_FOR_ENGINEER.concat(SQL_SEARCH) : SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH);
        try {

            ps = connection.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = connection.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ClaimHandlerGridDto claimHandlerDto = new ClaimHandlerGridDto();
                    claimHandlerDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    claimHandlerDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    claimHandlerDto.setPolicyNumberValue(rs.getString("t1.V_POL_NUMBER"));
                    claimHandlerDto.setInspectionStatus(0);
                    claimHandlerDto.setAccidentDate(rs.getString("t1.D_ACCID_DATE"));
                    claimHandlerDto.setAssignDateTime(Utility.getDate(rs.getString("t2.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                    claimHandlerDto.setDocumentStatus(null == rs.getString("t2.V_IS_ALL_DOC_UPLOAD") ? AppConstant.NO : rs.getString("t2.V_IS_ALL_DOC_UPLOAD"));
                    claimHandlerDto.setDocumentCheck(null == rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS") ? AppConstant.NO : rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS"));
                    claimHandlerDto.setLiabilityStatus(null == rs.getString("t2.V_LIABILITY_APRV_STATUS") ? AppConstant.NO : rs.getString("t2.V_LIABILITY_APRV_STATUS"));
                    claimHandlerDto.setClaimStatus(rs.getInt("t2.N_CLAIM_STATUS"));
                    claimHandlerDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                    claimHandlerDto.setTxnId(rs.getInt("t2.N_TXN_NO"));
                    claimHandlerDto.setAcr(rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT").toString());
                    claimHandlerDto.setPresentReverseAmount(rs.getBigDecimal("t2.N_RESERVE_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_RESERVE_AMOUNT").toString());
                    claimHandlerDto.setFileStore(rs.getString("t2.V_IS_FILE_STORE"));
                    claimHandlerDto.setPartialLoss(rs.getInt("t2.N_LOSS_TYPE"));
                    claimHandlerDto.setAssignUser(rs.getString("t2.V_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_ASSIGN_USER_ID"));
                    claimHandlerDto.setLiabilityAssignUser(rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER"));
                    claimHandlerDto.setLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT).equals(AppConstant.DEFAULT_DATE)
                            ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT));

                    claimHandlerDto.setIntLiabilityAssignUser(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID"));

                    claimHandlerDto.setIntLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                    claimHandlerDto.setInvestigationStatus(rs.getString("t2.V_INVESTIGATION_STATUS"));

                    claimHandlerDto.setIsDoubt(rs.getString("t2.V_IS_DOUBT"));
                    claimHandlerDto.setIsOnSiteOffer(rs.getString("t2.V_IS_ON_SITE_OFFER"));
                    claimHandlerDto.setAprvAdvanceAmount(rs.getBigDecimal("t2.N_APRV_ADVANCE_AMOUNT"));
                    claimHandlerDto.setReOpenType(rs.getString("t2.V_REOPEN_TYPE"));
                    claimHandlerDto.setCloseStatus(rs.getString("t2.V_CLOSE_STATUS"));
                    claimHandlerDto.setCloseUser(rs.getString("t2.V_CLOSE_USER"));
                    claimHandlerDto.setCloseDateTime(rs.getString("t2.D_CLOSE_DATE_TIME"));
                    claimHandlerDto.setPolicyChannelType(null == rs.getString("t1.V_POLICY_CHANNEL_TYPE") || rs.getString("t1.V_POLICY_CHANNEL_TYPE").trim().isEmpty() ? PolicyChannelType.CONVENTIONAL.name() : rs.getString("t1.V_POLICY_CHANNEL_TYPE"));
                    claimHandlerDto.setPriority(null == rs.getString("t1.V_PRIORITY") || rs.getString("t1.V_PRIORITY").isEmpty() ? AppConstant.PRIORITY_NORMAL : rs.getString("t1.V_PRIORITY"));
                    if (null != rs.getString("t2.V_CLOSE_STATUS") && rs.getString("t2.V_CLOSE_STATUS").equals("CLOSE")) {
                        claimHandlerDto.setFinalizeStatus(AppConstant.YES);
                    } else {
                        claimHandlerDto.setFinalizeStatus(AppConstant.NO);
                    }

                    if (claimHandlerDto.getClaimStatus() == 43 || claimHandlerDto.getClaimStatus() == 46 || claimHandlerDto.getClaimStatus() == 48 || claimHandlerDto.getClaimStatus() == 52
                            || claimHandlerDto.getClaimStatus() == 53 || claimHandlerDto.getClaimStatus() == 54 || claimHandlerDto.getClaimStatus() == 55 || claimHandlerDto.getClaimStatus() == 56 || claimHandlerDto.getClaimStatus() == 68) {
                        claimHandlerDto.setClaimStatusDesc("Pending Decision");
                    }


                    claimHandlerDto.setIndex(++index);
                    handlerList.add(claimHandlerDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRendomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public boolean isClaimForwardedToEngineer(Connection connection, Integer claimNo, String userId) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        boolean isForwarded = false;
        try {
            ps = connection.prepareStatement(null == userId ? IS_CLAIM_FORWARDED_TO_ENGINEER : IS_CLAIM_FORWARDED_TO_SELECTED_ENGINEER);
            ps.setInt(1, ClaimStatus.TASK_FORWARDED_TO_ENGINEER.getClaimStatus());
            ps.setInt(2, claimNo);
            if (null != userId) {
                ps.setString(3, userId);
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                isForwarded = true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return isForwarded;
    }

    @Override
    public String getAssignedDecisionMaker(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        String dmaker = AppConstant.STRING_EMPTY;
        try {
            ps = connection.prepareStatement(GET_ASSIGNED_DECISION_MAKER);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                dmaker = null == rs.getString("V_DECISION_MAKING_ASSIGN_USER_ID") ? AppConstant.STRING_EMPTY : rs.getString("V_DECISION_MAKING_ASSIGN_USER_ID");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return dmaker;
    }

    @Override
    public boolean isAutoStored(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        boolean isAutoStored = false;
        try {
            ps = connection.prepareStatement(IS_ARI_AND_STORED);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            isAutoStored = rs.next();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return isAutoStored;
    }

    @Override
    public ClaimPanelDto populatePanelStatus(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        ClaimPanelDto claimPanelDto = new ClaimPanelDto();
        try {
            ps = connection.prepareStatement(GET_PANEL_STATUS);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimPanelDto.setdMaker(rs.getString("V_DECISION_MAKING_ASSIGN_USER_ID"));
                String v_status = rs.getString("V_STATUS");
                switch (v_status) {
                    case "P":
                        v_status = "Pending";
                        break;
                    case "A":
                        v_status = "Approved";
                        break;
                    case "R":
                        v_status = "Rejected";
                        break;
                    case "D":
                        v_status = "Returned";
                        break;
                    default:
                        v_status = "N/A";
                        break;
                }
                claimPanelDto.setPanelDecision(v_status);
                claimPanelDto.setRejectionReason(rs.getString("REPUDIATED_REASON"));
                claimPanelDto.setDmComment(null == rs.getString("V_DM_REMARK") || rs.getString("V_DM_REMARK").isEmpty() ? "N/A" : rs.getString("V_DM_REMARK"));
                claimPanelDto.setIsPanelDecision(true);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return claimPanelDto;
    }

    @Override
    public String getARIRequestedFileAssignUser(Connection connection, int claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        String assignedUser = AppConstant.STRING_EMPTY;
        try {
            ps = connection.prepareStatement(GET_ARI_CLAIM_ASSIGNED_USER);
            rs = ps.executeQuery();
            while(rs.next()) {
                assignedUser = rs.getString("V_ASSIGNED_USER");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return assignedUser;
    }

    @Override
    public void updateClaimPanel(Connection connection, Integer claimNo, String panelId, String status) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_PANEL_DETAILS)) {
            ps.setInt(++index, Integer.parseInt(panelId));
            ps.setString(++index, status);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, claimNo);


            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<ClaimRepudiatedLetterTypeDto> getActiveRejectionReasons(Connection connection, String activeStatus) {
        String sql = CLAIM_REPUDIATED_LETTER_TYPE_LIST;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ClaimRepudiatedLetterTypeDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(sql);
            ps.setString(1, activeStatus);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimRepudiatedLetterTypeDto claimRepudiatedLetterTypeDto = new ClaimRepudiatedLetterTypeDto();
                claimRepudiatedLetterTypeDto.setRepudiatedLetterType(rs.getInt("N_REPUDIATE_LETTER_TYPE"));
                claimRepudiatedLetterTypeDto.setRepudiatedLetterTypeDesc(rs.getString("V_REPUDIATE_LETTER_TYPE_DESC"));
                list.add(claimRepudiatedLetterTypeDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public void attachRejectionFileOnOtherSelect(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(ATTACH_FILE_ON_OTHER_SELECT);
            ps.setInt(1, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @Override
    public int getRejectionRefNo(Connection connection, Integer claimNo, Integer rejectoinDocTypeId) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        Integer refNo = AppConstant.ZERO_INT;
        try {
            ps = connection.prepareStatement(GET_REJECTION_REF_NO);
            ps.setInt(1, claimNo);
            ps.setInt(2, rejectoinDocTypeId);
            rs = ps.executeQuery();
            while (rs.next()) {
                refNo = rs.getInt("N_REF_NO");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return refNo;
    }

    @Override
    public boolean checkAndUpdateAdvanceAmount(Connection connection, Integer claimNo, BigDecimal amount) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        PreparedStatement ps1;
        boolean isUpdated = false;
        try {
            ps = connection.prepareStatement(SELECT_APRV_ADVANCE_AMOUNT_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                BigDecimal advance = null == rs.getBigDecimal("N_APRV_ADVANCE_AMOUNT") ||
                        rs.getBigDecimal("N_APRV_ADVANCE_AMOUNT").toString().isEmpty() ?
                        BigDecimal.ZERO : rs.getBigDecimal("N_APRV_ADVANCE_AMOUNT");
                if (advance.signum() > 0) {
                    ps1 = connection.prepareStatement(UPDATE_APRV_ADVANCE_AMOUNT);
                    ps1.setBigDecimal(1, amount);
                    ps1.setInt(2, claimNo);
                    isUpdated = ps1.executeUpdate() > 0;
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return isUpdated;
    }

    @Override
    public Boolean checkClaimStatusByClaimNO(Connection connection,Integer claimNo) throws SQLException {
        PreparedStatement ps;
        ResultSet rs = null;
        try {

            ps = connection.prepareStatement(GET_N_CLAIM_STATUS_BY_CLAIM);
            ps.setInt(1,claimNo);
            rs=ps.executeQuery();

            if(rs.next()){
                return rs.getInt("N_CLAIM_STATUS") == AppConstant.CLAIM_STATUS_CLAIM_HANDLER_SPECIAL_COMMENT;
            }
            return false;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer getClaimStatusByClaimNo(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs = null;
        try {

            ps = connection.prepareStatement(GET_N_CLAIM_STATUS_BY_CLAIM);
            ps.setInt(1,claimNo);
            rs=ps.executeQuery();

            if (rs.next()) {
                return rs.getInt("N_CLAIM_STATUS");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return AppConstant.ZERO_INT;
    }

    @Override
    public void updateOldClaimStatus(Connection connection, int claimNo, int status) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(UPDATE_OLD_CLAIM_STATUS);
            ps.setInt(1, status);
            ps.setInt(2, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void updateReferenceTwoReserveAcrAmount(BigDecimal reseveAmt, BigDecimal reseveAmtAfterApr, Connection connection, Integer claimNo, int type) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(SQL_UPDATE_REFERENCE_TWO_RESERVE_AMOUNT_CLAIM_NO)) {
            ps.setBigDecimal(1, reseveAmt);
            ps.setBigDecimal(2, reseveAmtAfterApr);
            ps.setInt(3, claimNo);
            ps.setInt(4, type);

            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public void insertReferenceTwoReserveAcrAmount(BigDecimal reseveAmt, BigDecimal reseveAmtAfterApr, Connection connection, Integer claimNo,Integer calSheeType,String code) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(SQL_INSERT_REFERENCE_TWO_RESERVE_AMOUNT_CLAIM_NO)) {
            ps.setInt(1, claimNo);
            ps.setString(2, code);
            ps.setInt(3, calSheeType);
            ps.setBigDecimal(4, reseveAmt);
            ps.setBigDecimal(5, reseveAmtAfterApr);
            ps.setBigDecimal(6, reseveAmt);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public BigDecimal getReferenceTwoReserveAmount(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        BigDecimal reserveAmountAfterApproved = BigDecimal.ZERO;
        try {
            ps = connection.prepareStatement(SELECT_REFERENCE_TWO_RESERVE_AMOUNT_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            if (rs.next()) {
                reserveAmountAfterApproved = rs.getBigDecimal("N_RESERVE_AMOUNT");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return reserveAmountAfterApproved;
    }

    @Override
    public void updateReferenceTwoReserveAcrAmountAfetrApproved(BigDecimal reseveAmtAfterApr, Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_APPROVED_UPDATED_REFERENCE_TWO_RESEVE_AMOUNT_CLAIM_NO);
            ps.setBigDecimal(1, reseveAmtAfterApr);
            ps.setInt(2, claimNo);

            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public void updateReferenceTwoReserveAmountAndReserveAmountAfterApproved(Connection connection, BigDecimal reserveAmount, BigDecimal reserveAmountAfterApproved, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_REFERENCE_TWO_RESERVE_AMOUNT_AND_AFTER_APPROVED);
            ps.setBigDecimal(1, reserveAmount);
            ps.setBigDecimal(2, reserveAmountAfterApproved);
            ps.setInt(3, claimNo);

            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;

        }
    }

    @Override
    public void updateReferenceTwoReserveAmountAfterApproved(Connection connection, BigDecimal reserveAmountAfterApproved, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_REFERENCE_TWO_RESERVE_AMOUNT_AFTER_APPROVED_BY_CLAIM_NO);
            ps.setBigDecimal(1, reserveAmountAfterApproved);
            ps.setInt(2, claimNo);

            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public CalSheetTypeDto getReferenceTwoReserve(Connection connection, Integer claimNo, Integer calSheetType) throws Exception {
        PreparedStatement ps = null;
        ResultSet resultSet;
        CalSheetTypeDto calSheetTypeDto = new CalSheetTypeDto();
        try {
            ps = connection.prepareStatement(SELECT_RESERVE_AMOUNT_DETAIL);
            ps.setInt(1, claimNo);
            ps.setInt(2, calSheetType);
            resultSet = ps.executeQuery();
            if(resultSet.next()){
                 calSheetTypeDto.setCode(resultSet.getString("V_CODE"));
                 calSheetTypeDto.setLimit(resultSet.getBigDecimal("N_RESERVE_AMOUNT"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return calSheetTypeDto;
    }

    @Override
    public List<InvestigatorDetailsDto> getInvestigatorList(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<InvestigatorDetailsDto> investigatorDetailsList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(GET_INVESTIGATOR_LIST);
            rs = ps.executeQuery();
            while (rs.next()) {
                InvestigatorDetailsDto investigator = new InvestigatorDetailsDto();
                investigator.setRefNo(rs.getInt("N_REF_NO"));
                investigator.setCode(rs.getString("V_CODE"));
                investigator.setName(rs.getString("V_NAME"));
                investigator.setEmail(rs.getString("V_EMAIL"));
                investigator.setMobile(rs.getString("N_MOBILE"));
                investigatorDetailsList.add(investigator);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return investigatorDetailsList;
    }
}
