package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.DocumentContentDao;
import com.misyn.mcms.claim.dto.DocumentContentDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class DocumentContentDaoImpl implements DocumentContentDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(DocumentContentDaoImpl.class);


    @Override
    public DocumentContentDetails searchDocumentContentDetail(Connection connection, String subject) {
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            // Query by subject
            String sql = "SELECT document_content_id, document_body, document_status, document_subject " +
                    "FROM document_content_details WHERE document_subject = ?";

            ps = connection.prepareStatement(sql);
            ps.setString(1, subject); // Example: "Breach of Policy Condition"

            rs = ps.executeQuery();
            if (rs.next()) {
                DocumentContentDetails documentContentDetails = new DocumentContentDetails();
                documentContentDetails.setDocumentBody(rs.getString("document_body"));
                documentContentDetails.setDocumentStatus(rs.getString("document_status"));
                documentContentDetails.setDocumentSubject(rs.getString("document_subject"));
                return documentContentDetails;
            }
        } catch (Exception e) {
            LOGGER.error("Error while fetching document content details", e);
        } finally {
            try {
                if (rs != null) rs.close();
                if (ps != null) ps.close();
            } catch (Exception e) {
                LOGGER.error("Error closing resources", e);
            }
        }
        return null;
    }



    @Override
    public String getAppConstantNameByEnum(Connection connection, String enumName) {
        String sql = "SELECT appconst_name FROM check_doc_type WHERE enum_name = ?";
        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.setString(1, enumName);  // ✅ now parameter matches
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("appconst_name");
                }
            }
        } catch (Exception e) {
            e.printStackTrace(); // or use LOGGER.error(e)
        }
        return null;
    }

}
