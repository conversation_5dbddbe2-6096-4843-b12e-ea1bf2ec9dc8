package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.CalSheetTypeDto;
import com.misyn.mcms.claim.dto.CalculationSheetHistoryDto;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetMainDto;
import com.misyn.mcms.claim.dto.UserDto;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;

public interface ClaimCalculationSheetMainDao extends BaseDao<ClaimCalculationSheetMainDto> {

    String CLAIM_CALCULATION_SHEET_MAIN_INSERT = "INSERT INTO claim_calculation_sheet_main VALUES (0,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    String CLAIM_CALCULATION_SHEET_MAIN_UPDATE = "UPDATE claim_calculation_sheet_main \n" +
            "SET N_CLAIM_NO =?,\n" +
            "N_LOSS_TYPE =?,\n" +
            "N_CAUSE_OF_LOSS =?,\n" +
            "N_PAYMENT_TYPE =?,\n" +
            "N_CAL_SHEET_TYPE =?,\n" +
            "N_LABOUR =?,\n" +
            "N_PARTS =?,\n" +
            "N_TOTAL_LABOUR_AND_PARTS =?,\n" +
            "N_POLICY_EXCESS =?,\n" +
            "N_UNDER_INSURANCE_RATE =?,\n" +
            "N_UNDER_INSURANCE =?,\n" +
            "N_BALD_TYRE_RATE =?,\n" +
            "N_BALD_TYRE =?,\n" +
            "N_SPECIAL_DEDUCTIONS =?,\n" +
            "N_SPECIAL_VAT_AMOUNT =?,\n" +
            "N_SPECIAL_NBT_AMOUNT =?,\n" +
            "N_TOTAL_DEDUCTIONS =?,\n" +
            "N_TOTAL_AFTER_DEDUCTIONS =?,\n" +
            "N_PAID_ADVANCE_AMOUNT =?,\n" +
            "N_PAYABLE_AMOUNT =?,\n" +
            "V_REMARK =?,\n" +
            "V_STATUS =?,\n" +
            "V_NO_OBJECTION_STATUS =?,\n" +
            "V_PREMIUM_OUTSTANDING_STATUS =?,\n" +
            "V_IS_EXCESS_INCLUDE =?,\n" +
            "V_VOUCHER_NO =?,\n" +
            "V_VOUCHER_GENERATED_USER =?,\n" +
            "D_VOUCHER_GENERATED_DATETIME =?,\n" +
            "V_INPUT_USER =?,\n" +
            "D_INPUT_DATETIME =?,\n" +
            "V_ASSIGN_USER_ID =?,\n" +
            "D_ASSIGN_DATE_TIME =?,\n" +
            "V_APR_USER =?,\n" +
            "D_APR_DATE_TIME =?,\n" +
            "V_APR_ASSIGN_USER =?,\n" +
            "D_APR_ASSIGN_DATE_TIME = ?,\n" +
            "V_CHECKED_USER =?,\n" +
            "D_CHECKED_DATE_TIME = ?,\n" +
            "V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID =?,\n" +
            "D_SPARE_PART_CORDINATOR_ASSIGN_DATE_TIME = ?,\n" +
            "V_SCRUTINIZE_TEAM_ASSIGN_USER_ID =?,\n" +
            "D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME = ?,\n" +
            "V_SPECIAL_TEAM_ASSIGN_USER_ID =?,\n" +
            "D_SPECIAL_TEAM_ASSIGN_DATE_TIME = ?,\n" +
            "V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID =?,\n" +
            "D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME = ?,\n" +
            "V_NCB_STATUS = ?,\n" +
            "V_RTE_ASSIGN_USER_ID = ?,\n" +
            "D_RTE_ASSIGN_DATE_TIME = ?,\n" +
            "V_IS_ADJUST_VAT_AMOUNT = ?,\n" +
            "N_ADJUST_VAT_AMOUNT = ?,\n" +
            "V_IS_ADJUST_NBT_AMOUNT = ?,\n" +
            "N_ADJUST_NBT_AMOUNT = ? \n" +
            "WHERE\n" +
            "	N_CAL_SHEET_ID =?";
    String CLAIM_CALCULATION_SHEET_MAIN_SEARCH = "SELECT * FROM claim_calculation_sheet_main WHERE N_CAL_SHEET_ID =?";
    String CLAIM_CALCULATION_SHEET_MAIN_SEARCH_ALL = " SELECT * FROM claim_calculation_sheet_main";
    String CLAIM_CALCULATION_SHEET_MAIN_DELETE = "DELETE FROM claim_calculation_sheet_main WHERE N_CAL_SHEET_ID =?";
    String CLAIM_CALCULATION_SHEET_MAIN_SEARCH_ALL_BY_CLAIM_NO = "SELECT\n" +
            "t1.*,\n" +
            "t2.V_PAYMENT_TYPE_DESC,\n" +
            "t3.V_CAL_SHEET_TYPE_DESC\n" +
            "FROM\n" +
            "claim_calculation_sheet_main AS t1\n" +
            "INNER JOIN claim_payment_type AS t2 ON t1.N_PAYMENT_TYPE = t2.N_ID\n" +
            "INNER JOIN claim_calculation_sheet_type AS t3 ON t1.N_CAL_SHEET_TYPE = t3.N_CAL_SHEET_TYPE_ID\n" +
            "WHERE\n" +
            "t1.N_CLAIM_NO=?";

    String CLAIM_CALCULATION_SHEET_MAIN_SEARCH_ALL_BY_N_SUPPLIER_ORDER_ID = "SELECT\n" +
            "t1.N_CAL_SHEET_ID,\n" +
            "t1.N_CLAIM_NO,\n" +
            "t1.N_CAL_SHEET_TYPE,\n" +
            "t1.V_STATUS,\n" +
            "t2.N_SUPPLIER_ORDER_ID\n" +
            "FROM\n" +
            "claim_calculation_sheet_main AS t1\n" +
            "INNER JOIN claim_calculation_sheet_supplier_order AS t2 ON t2.N_CAL_SHEET_ID = t1.N_CAL_SHEET_ID\n" +
            "WHERE\n" +
            "t2.N_SUPPLIER_ORDER_ID = ? AND\n" +
            "t1.V_STATUS = 65";

    String SELECT_CALCULATION_MAIN_BY_CLIM_NO = "SELECT N_CAL_SHEET_ID FROM claim_calculation_sheet_main WHERE N_CLAIM_NO =?";

    String SELECT_CAL_SHEET_TYPES = "SELECT * FROM claim_calculation_sheet_type WHERE V_REC_STATUS = 'A'";

    String SELECT_SPECIAL_TEAM_USER_ID = "SELECT V_SPECIAL_TEAM_ASSIGN_USER_ID FROM claim_calculation_sheet_main WHERE N_CLAIM_NO =? and V_STATUS=? and N_CAL_SHEET_TYPE=?";

    String SELECT_NO_OBJECTION_STATUS_AND_UPLOAD_BY_CLAIM_ID = "SELECT 1 \n" +
            "FROM claim_calculation_sheet_main \n" +
            "WHERE N_CLAIM_NO = ? \n" +
            "AND V_NO_OBJECTION_STATUS='C'\n" +
            "AND V_IS_NO_OBJECTION_UPLOAD IN('P','R','U')";

    String SELECT_PREMIUM_OUTSTANDING_STATUS_AND_UPLOAD_BY_CLAIM_ID = "SELECT 1 \n" +
            "FROM claim_calculation_sheet_main \n" +
            "WHERE N_CLAIM_NO = ? \n" +
            "AND V_PREMIUM_OUTSTANDING_STATUS='C'\n" +
            "AND V_IS_PREMIUM_OUTSTANDING_UPLOAD IN('P','R','U')";

    String UPDATE_NO_OBJECTION_DOC_REF_NO = "UPDATE claim_calculation_sheet_main SET N_NO_OBJECTION_DOC_REF_NO=?, V_IS_NO_OBJECTION_UPLOAD='U' WHERE N_CLAIM_NO = ?";

    String UPDATE_PREMIUM_OUTSTANDING_DOC_REF_NO = "UPDATE claim_calculation_sheet_main SET N_PREMIUM_OUTSTANDING_DOC_REF_NO=? , V_IS_PREMIUM_OUTSTANDING_UPLOAD='U' WHERE N_CLAIM_NO = ?";

    String SELECT_CLAIM_NO_BY_CAL_SHEET_ID = "SELECT N_CLAIM_NO FROM claim_calculation_sheet_main WHERE N_CAL_SHEET_ID = ?";

    String UPDATE_ASSIGN_USER_BY_CLAIM_NO = "UPDATE claim_calculation_sheet_main SET V_ASSIGN_USER_ID =? WHERE N_CLAIM_NO=? AND V_STATUS NOT IN(67,70)";

    String SELECT_N_POLICY_EXCESS_GREATER_THAN_ZERO_BY_CLAIM_NO = "SELECT 1 FROM claim_calculation_sheet_main WHERE N_CLAIM_NO=? AND N_POLICY_EXCESS >0 AND V_STATUS NOT IN (66, 73) AND N_CAL_SHEET_ID <> ?";

    String SELECT_PENDING_CALSHEET_BY_CALSHEET_ID = "SELECT 1 FROM claim_calculation_sheet_main WHERE  N_CAL_SHEET_ID = ? AND V_STATUS = 58";

    String CLAIM_CALCULATION_SHEET_MAIN_SEARCH_ALL_BY_CLAIM_NO_ORDER_BY_DESC = "SELECT\n" +
            "            t1.*,\n" +
            "            t2.V_PAYMENT_TYPE_DESC,\n" +
            "            t3.V_CAL_SHEET_TYPE_DESC\n" +
            "            FROM\n" +
            "            claim_calculation_sheet_main AS t1\n" +
            "            INNER JOIN claim_payment_type AS t2 ON t1.N_PAYMENT_TYPE = t2.N_ID\n" +
            "            INNER JOIN claim_calculation_sheet_type AS t3 ON t1.N_CAL_SHEET_TYPE = t3.N_CAL_SHEET_TYPE_ID\n" +
            "            WHERE\n" +
            "            t1.N_CLAIM_NO=? ORDER BY t1.N_CAL_SHEET_ID DESC";

    String SUM_OF_TOTAL_PAID_ADVANCE_AMOUNT = "SELECT\n" +
            "	sum(N_PAYABLE_AMOUNT) AS tot\n" +
            "FROM\n" +
            "	claim_calculation_sheet_main\n" +
            "WHERE\n" +
            "	N_CLAIM_NO = ?\n" +
            "AND N_CAL_SHEET_TYPE =2 AND V_STATUS IN(65,67,70)";

    String SELECT_ONE_FROM_CALSHEET_APPROVED_OR_FORWARD_TO_APPROVAL = "SELECT 1 FROM claim_calculation_sheet_main WHERE N_CLAIM_NO =? AND V_STATUS IN(63,65)";

    String SELECT_ALL_FROM_CALSHEET_BY_CLAIM_NO_AND_VOUCHER_GENERATED_AND_PENDING = "SELECT * FROM claim_calculation_sheet_main WHERE N_CLAIM_NO = ? AND V_STATUS NOT IN(67,70)";

    String UPDATE_SPECIAL_TEAM_AND_MOFA_ASSIGN_USER_BY_CALSHEET_ID = "UPDATE claim_calculation_sheet_main \n" +
            "SET V_SPECIAL_TEAM_ASSIGN_USER_ID = ?,\n" +
            "D_SPECIAL_TEAM_ASSIGN_DATE_TIME = ?,\n" +
            "V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID = ?,\n" +
            "D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME = ? \n" +
            "WHERE\n" +
            "	N_CAL_SHEET_ID = ?";

    String UPDATE_SPECIAL_TEAM_ASSIGN_USER_BY_CALSHEET_ID = "UPDATE claim_calculation_sheet_main \n" +
            "SET V_SPECIAL_TEAM_ASSIGN_USER_ID = ?,\n" +
            "D_SPECIAL_TEAM_ASSIGN_DATE_TIME = ?,\n" +
            "WHERE\n" +
            "	N_CAL_SHEET_ID = ?";

    String SELECT_MOFA_USER = "SELECT\n" +
            "	t1.v_usrid,\n" +
            "	t1.n_usrcode,\n" +
            "	t1.v_firstname,\n" +
            "	t1.v_lastname,\n" +
            "	t1.N_PAYMENT_AUTH_LIMIT \n" +
            "FROM\n" +
            "	usr_mst AS t1 \n" +
            "WHERE\n" +
            "	t1.n_accessusrtype IN ( ?) \n" +
            "	AND t1.N_PAYMENT_AUTH_LIMIT >= ?\n" +
            "	ORDER BY N_PAYMENT_AUTH_LIMIT ASC LIMIT 1";

    String GET_SPARE_PARTS_COORDINATOR_FROM_CALSHEET_BY_CLAIM_NO = "select N_CAL_SHEET_ID, V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID from claim_calculation_sheet_main WHERE V_STATUS IN(59) AND N_CLAIM_NO = ?";

    String GET_SCRUTINIZING_USER_FROM_CALSHEET_BY_CLAIM_NO = "select N_CAL_SHEET_ID, V_SCRUTINIZE_TEAM_ASSIGN_USER_ID from claim_calculation_sheet_main WHERE V_STATUS IN(61) AND N_CLAIM_NO = ?";

    String SELECT_SUM_OF_APPROVED_CALSHEET_PAYABLE_AMOUNT = "SELECT\n" +
            "	sum(N_PAYABLE_AMOUNT) AS tot\n" +
            "FROM\n" +
            "	claim_calculation_sheet_main\n" +
            "WHERE\n" +
            "	N_CLAIM_NO = ? AND V_STATUS IN(67,70)";

    String SELECT_ASSIGN_USER_ID_BY_CALSHEET_ID = "SELECT V_ASSIGN_USER_ID FROM claim_calculation_sheet_main WHERE N_CAL_SHEET_ID = ?";

    String SELECT_ONE_BY_CLAIM_NO_AND_STATUS = "SELECT 1 FROM claim_calculation_sheet_main WHERE N_CLAIM_NO = ? AND V_STATUS = ?";

    String SELECT_LATEST_CALSHEET = "SELECT" +
            " V_STATUS," +
            " V_VOUCHER_GENERATED_USER," +
            " V_INPUT_USER," +
            " V_ASSIGN_USER_ID," +
            " V_APR_USER," +
            " V_APR_ASSIGN_USER," +
            " V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID," +
            " V_SCRUTINIZE_TEAM_ASSIGN_USER_ID," +
            " V_SPECIAL_TEAM_ASSIGN_USER_ID," +
            " V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID," +
            " V_RELEASE_ORDER_GENERATE_USER_ID" +
            " FROM claim_calculation_sheet_main WHERE N_CLAIM_NO=? ORDER BY N_CAL_SHEET_ID DESC LIMIT 1";

    String SELECT_ALL_BY_CLAIM_NO_NOT_IN_REJECTED = "SELECT\n" +
            "\tt1.N_CAL_SHEET_ID,\n" +
            "\tt1.N_CAL_SHEET_TYPE,\n" +
            "\tt2.V_CAL_SHEET_TYPE_DESC,\n" +
            "\tt1.N_PAYABLE_AMOUNT,\n" +
            "\tt1.D_VOUCHER_GENERATED_DATETIME,\n" +
            "\tt1.V_ASSIGN_USER_ID,\n" +
            "\tt1.V_SPECIAL_TEAM_ASSIGN_USER_ID,\n" +
            "\tt1.V_APR_USER,\n" +
            "\tt1.V_STATUS,\n" +
            "\tt1.V_RTE_ACTION,\n" +
            "\tt1.V_RTE_ASSIGN_USER_ID,\n" +
            "\tt1.V_VOUCHER_GENERATED_USER,\n" +
            "\tt4.V_ACR_APRV_USER\n" +
            "FROM\n" +
            "\tclaim_calculation_sheet_main AS t1\n" +
            "\tINNER JOIN claim_calculation_sheet_type AS t2 ON t1.N_CAL_SHEET_TYPE = t2.N_CAL_SHEET_TYPE_ID\n" +
            "\tINNER JOIN claim_status_para AS t3 ON t1.V_STATUS = t3.n_ref_id\n" +
            "\tINNER JOIN claim_assign_claim_handler AS t4 ON t1.N_CLAIM_NO = t4.N_CLAIM_NO\n" +
            "WHERE\n" +
            "\tt1.N_CLAIM_NO = ?\n" +
            "\tAND t1.V_STATUS NOT IN ( 66, 73 )";

    String UPDATE_RTE_ACTION_BY_CALSHEET_ID = "UPDATE claim_calculation_sheet_main \n" +
            "SET V_RTE_ACTION = ? \n" +
            "WHERE\n" +
            "	N_CAL_SHEET_ID = ?";

    String SELECT_TOT_PAYABLE_AMOUNT = "SELECT\n" +
            "	sum( N_PAYABLE_AMOUNT ) AS tot \n" +
            "FROM\n" +
            "	claim_calculation_sheet_main \n" +
            "WHERE\n" +
            "	N_CLAIM_NO = ? \n" +
            "	AND V_STATUS NOT IN ( 66 ,72, 73 )";

    String CANCEL_BALANCE_CALSHEET_BY_CLAIM_NO = "UPDATE claim_calculation_sheet_main \n" +
            "SET V_STATUS = ? \n" +
            "WHERE\n" +
            "	N_CLAIM_NO = ? \n" +
            "	AND V_STATUS NOT IN ( 66, 67, 70 )";


    String SELECT_PENDING_BALANCE_CALSHEET = "SELECT\n" +
            "	t1.*,\n" +
            "	t2.V_CAL_SHEET_TYPE_DESC\n" +
            "FROM\n" +
            "	claim_calculation_sheet_main  AS t1\n" +
            "	INNER JOIN claim_calculation_sheet_type AS t2\n" +
            "WHERE\n" +
            "	N_CLAIM_NO = ?\n" +
            "	AND N_CAL_SHEET_TYPE IN ( 4, 7 ) \n" +
            "	AND V_STATUS NOT IN ( 66, 67, 70, 73 )";

    String GET_RTE_ACTION = "SELECT V_RTE_ACTION FROM claim_calculation_sheet_main WHERE N_CAL_SHEET_ID = ?";

    String UPDATE_MOTOR_ENGINEER = "UPDATE claim_calculation_sheet_main SET V_STATUS = ?, V_RTE_ASSIGN_USER_ID = ?, D_RTE_ASSIGN_DATE_TIME = ? WHERE N_CAL_SHEET_ID = ?";

    String IS_AVAILABLE_CAL_SHEET_BY_TYPE = "SELECT N_CAL_SHEET_ID FROM claim_calculation_sheet_main WHERE N_CAL_SHEET_TYPE = ? AND N_CLAIM_NO = ? AND V_STATUS NOT IN ( 66 ,72 , 73 )";

    String SELECT_TOT_PAID_AMOUNT = "SELECT\n" +
            "	sum( N_PAYABLE_AMOUNT ) AS tot \n" +
            "FROM\n" +
            "	claim_calculation_sheet_main \n" +
            "WHERE\n" +
            "	N_CLAIM_NO = ? \n" +
            "   AND N_CAL_SHEET_ID <> ? \n" +
            "	AND V_STATUS NOT IN ( 66 ,72, 73 )";

    String GET_ASSIGN_RTE = "SELECT V_RTE_ASSIGN_USER_ID FROM claim_calculation_sheet_main WHERE N_CAL_SHEET_ID = ?";

    String IS_VOU_GEN_CAL_SHEET_FOR_DO = "SELECT 1\n" +
            " FROM claim_calculation_sheet_main AS t1\n" +
            " INNER JOIN claim_calculation_sheet_supplier_order AS t2 ON t2.N_CAL_SHEET_ID = t1.N_CAL_SHEET_ID\n" +
            " WHERE t2.N_SUPPLIER_ORDER_ID = ?\n" +
            " AND t1.V_STATUS IN (67, 70)";

    String IS_LOSS_CALSHEET = "SELECT N_CAL_SHEET_ID from claim_calculation_sheet_main WHERE N_LOSS_TYPE IN (2, 7) AND N_CAL_SHEET_ID = ?";

    String CALSHEET_FOR_BILL_CHECKING = "SELECT N_CAL_SHEET_ID FROM claim_calculation_sheet_main WHERE N_CLAIM_NO = ? AND V_STATUS IN (61, 59)";

    String GET_POLICY_CHANNEL_TYPE_BY_CLAIM_NO = "SELECT V_POLICY_CHANNEL_TYPE FROM claim_claim_info_main WHERE N_CLIM_NO = ?";

    String UPDATE_CLAIM_STATUS_BY_TXNID = "UPDATE claim_calculation_sheet_main SET V_STATUS=? WHERE N_CLAIM_NO=?";

    String IS_PENDING_PAYMENT = "SELECT 1 FROM claim_calculation_sheet_main WHERE N_CLAIM_NO = ? AND V_STATUS NOT IN (65, 66, 73, 67, 70) AND N_CAL_SHEET_TYPE NOT IN (9,10,11)";

    List<ClaimCalculationSheetMainDto> searchByClaimNo(Connection connection, Integer claimNo) throws Exception;

    void updateSparePartCordAssignUser(Connection connection, Integer calSheetId, String userId, String sysDateTime) throws Exception;

    void updateReleaseOrderDetails(Connection connection, int calSheetId, String isReleaseOrderGenerate, String releaseOrderGenerateUserId) throws Exception;

    void updateScrutinizingTeamAssignUser(Connection connection, Integer calSheetId, String userId, String sysDateTime) throws Exception;

    void updateSpecialTeamAssignUser(Connection connection, Integer calSheetId, String userId, String sysDateTime) throws Exception;

    void updateMofaTeamAssignUser(Connection connection, Integer calSheetId, String userId, String sysDateTime) throws Exception;

    void updateStatus(Connection connection, Integer calSheetId, Integer status) throws Exception;

    void updatePaymentApproveStatus(Connection connection, Integer calSheetId, Integer status, String userId) throws Exception;

    void updateSparePartCoordinatorAssignDetails(Connection connection, Integer calSheetId, Integer status, String sparePartCoordinatorUserId) throws Exception;

    void updateVoucherNo(Connection connection, Integer calSheetId, String voucherNo) throws Exception;

    void updateVoucherNo(Connection connection, Integer calSheetId, String voucherNo, String generatedUser, String generatedDateTime) throws Exception;

    void updateNoObjectionStatus(Connection connection, Integer calSheetId, String status) throws Exception;

    void updateNoObjectionUploadReject(Connection connection, Integer claimNo, Integer docRefNo) throws Exception;

    void updatePremiumOutstandingUploadReject(Connection connection, Integer claimNo, Integer docRefNo) throws Exception;

    void updatePremiumOutstandingStatus(Connection connection, Integer calSheetId, String status) throws Exception;

    List<UserDto> getMofaUserList(Connection connection, String amount, int accessUserType) throws Exception;

    Integer isSupplyOrderCalculationSheetApproved(Connection connection, Integer supplyOrderId);

    boolean isAvailableCalculationSheet(Connection connection, Integer claimNo);

    List<CalSheetTypeDto> getCalSheetType(Connection connection) throws Exception;

    String getSpecialTeamAssignUserId(Connection connection, Integer ClaimNo, Integer Status, Integer calSheetType) throws Exception;

    void updateClaSheetPrintStatus(Connection connection, Integer calSheetId, String user, String dateTime) throws Exception;

    BigDecimal getTotalPaybleAmount(Connection connection, int claimNo, int ignoreCalSheetNo);

    void updateStatusVoucherGenerate(Connection connection, Integer calSheetId, Integer status, String userId) throws Exception;

    boolean isFoundPendingDocUploadNoObjection(Connection connection, int claimNo) throws Exception;

    boolean isFoundPendingDocUploadPremiumOutstanding(Connection connection, int claimNo) throws Exception;

    void updateNoObjectionDocRefNo(Connection connection, int claimNo, int docRefNo) throws Exception;

    void updatePremiumOutstandingDocRefNo(Connection connection, int claimNo, int docRefNo) throws Exception;

    Integer getClaimNoByCalSheetId(Connection connection, int calsheetId) throws Exception;

    void updateCalsheetAssignUser(Connection connection, String assignUserId, Integer claimNo) throws Exception;

    boolean isExcessAlreadyApply(Connection connection, Integer claimNo, Integer calSheetId) throws Exception;

    boolean isPendingCalsheet(Connection connection, Integer calSheetId) throws Exception;

    List<ClaimCalculationSheetMainDto> searchByClaimNoOrderByDesc(Connection connection, Integer claimNo) throws Exception;

    BigDecimal getPaidTotalAdvanceAmount(Connection connection, int claimNo);

    boolean isPaymentForwardedToApprovalOrApprove(Connection connection, int claimNo);

    List<Integer> getCalSheetIdsByClaimNo(Connection connection, int claimNo);

    List<ClaimCalculationSheetMainDto> searchByClaimNoAndNotInVoucherGeneratedOrPending(Connection connection, int claimNo);

    void assignSpTeamAndMofaTeam(Connection connection, ClaimCalculationSheetMainDto updateCalculationShettMainDto) throws Exception;

    void assignSpTeamTeam(Connection connection, Integer calSheetId, String userId) throws Exception;

    String getMofaUser(Connection connection, BigDecimal payableAmount, Integer accessUserType) throws Exception;

    ClaimCalculationSheetMainDto getSparePartsCoordFromClaimNo(Connection connection, Integer claimNo);

    ClaimCalculationSheetMainDto getScrutinizingUserFromClaimNo(Connection connection, Integer claimNo);

    BigDecimal getTotalApprovedPaymentAmount(Connection connection, Integer claimNo) throws Exception;

    void updateNoClaimBonusStatus(Connection connection, Integer calSheetId, String status) throws Exception;

    String getAssignUserId(Connection connection, Integer calSheetId);

    boolean isAvailableCalSheetByStatus(Connection connection, int claimNo, int claimStatus);

    ClaimCalculationSheetMainDto getLatestCalsheet(Connection connection, Integer claimNo) throws Exception;

    List<CalculationSheetHistoryDto> getCalculationSheetListByClaimNo(Connection connection, Integer claimNo);

    void updateRteAction(Connection connection, Integer calSheetId, String rteAction) throws Exception;

    BigDecimal getTotalPayableAmount(Connection connection, Integer claimNo);

    ClaimCalculationSheetMainDto getPendingBalanceCalsheet(Connection connection, Integer claimNo);

    String getRteAction(Connection connection, Integer calsheetId) throws Exception;

    void updateMotorEngineer(Connection connection, Integer calSheetId, String rteAssignUserId, String curDate) throws Exception;

    Integer isAvailableCalSheetByType(Connection connection, Integer claimNo, Integer calSheetType) throws Exception;

    BigDecimal getTotalPayableAmount(Connection connection, Integer claimNo, Integer calSheetId);

    String getAssignRte(Connection connection, Integer calSheetId) throws Exception;

    boolean isHavingVoucherGeneratedCalsheetForDo(Connection connection, Integer supplyOrderRefNo) throws Exception;

    boolean getIsLoss(Connection connection, Integer calSheetId) throws Exception;

    String getPolicyChannelType(Connection connection, Integer claimNo) throws Exception;

    void updateClaimNumberByTxnId(Connection connection , Integer claimNo ,Integer claimStatus)throws Exception;

    Integer searchByClaimNoForBillChecking(Connection connection, Integer claimNo) throws Exception;

    boolean checkPendingPayment(Connection connection, Integer claimNo) throws Exception;
}
